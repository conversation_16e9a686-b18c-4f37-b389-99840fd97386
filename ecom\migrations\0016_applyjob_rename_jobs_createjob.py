# Generated by Django 5.2.3 on 2025-07-13 08:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ecom', '0015_jobs_delete_feedback'),
    ]

    operations = [
        migrations.CreateModel(
            name='Apply<PERSON><PERSON>',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=150)),
                ('email', models.EmailField(max_length=150)),
                ('contact_number', models.IntegerField(max_length=15)),
                ('gender', models.Char<PERSON>ield(choices=[('M', 'Male'), ('F', 'Female')], max_length=1)),
                ('dob', models.DateField()),
                ('job_type', models.CharField(choices=[('JV', 'Java'), ('PY', 'Python')], max_length=2)),
                ('resume', models.<PERSON><PERSON><PERSON><PERSON>(max_length=500)),
            ],
        ),
        migrations.RenameModel(
            old_name='Jobs',
            new_name='<PERSON>reate<PERSON><PERSON>',
        ),
    ]
