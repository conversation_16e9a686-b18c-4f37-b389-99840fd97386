{% extends 'ecom/homebase.html' %}
{% load static %}
{% block content %}


<head>
  <link href="//netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap.min.css" rel="stylesheet" id="bootstrap-css">
  <script src="//netdna.bootstrapcdn.com/bootstrap/3.0.0/js/bootstrap.min.js"></script>
  <script src="//code.jquery.com/jquery-1.11.1.min.js"></script>
<style media="screen">
  .button {
  display: inline-block;
  border-radius: 8px;
  background: linear-gradient(135deg, #ff7043 0%, #ff8a65 100%);
  border: none;
  color: #FFFFFF;
  text-align: center;
  font-size: 28px;
  padding: 20px;
  width: 200px;
  transition: all 0.3s ease;
  cursor: pointer;
  margin: 5px;
  box-shadow: 0 4px 16px rgba(255, 112, 67, 0.3);
  font-weight: 600;
}

.button span {
  cursor: pointer;
  display: inline-block;
  position: relative;
  transition: 0.5s;
}

.button span:after {
  content: '\00bb';
  position: absolute;
  opacity: 0;
  top: 0;
  right: -20px;
  transition: 0.5s;
}

.button:hover span {
  padding-right: 25px;
}

.button:hover span:after {
  opacity: 1;
  right: 0;
}

.button2 {
  background: linear-gradient(135deg, #ffffff 0%, #f5f5f5 100%);
  color: #1a237e;
  border: 2px solid #00acc1;
  font-weight: 600;
}

.button2:hover {
  background: linear-gradient(135deg, #00acc1 0%, #00bcd4 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 172, 193, 0.3);
}

</style>

</head>
<br><br><br><br>

<div class="container">
  <div class="panel panel-primary">
    <div class="panel-heading" style="background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%); border-radius: 8px 8px 0 0;">
      <h6 style="text-align:center;" class="panel-title">My Cart</h6>
    </div>
    <table class="table table-hover" id="dev-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Image</th>
          <th>Price</th>
          <th>Description</th>
          <th>Remove From Cart</th>

        </tr>
      </thead>
      {% for p in products %}
  {% if p %}

      <tr>
        <td> {{p.name}}</td>
        <td>
        {% if p.get_image_url %}
        <img src="{{ p.get_image_url }}" alt="Product Image" height="40px" width="40px" />
        {% else %}
        <img src="{% static 'images/default-product.png' %}" alt="Product Image" height="40px" width="40px" />
        {% endif %}
        </td>
        <td>{{p.price}}</td>
        <td>{{p.description}}</td>

        <td><a class="btn btn-danger btn-xs" href="{% url 'remove-from-cart' p.id  %}"><span class="glyphicon glyphicon-trash"></span></a></td>

      </tr>
      {%else%}
      <br><br><br>
      <h1>no products</h1>
      {% endif %}
      {% endfor %}
    </table>
  </div>
</div>
<br><br><br>
<div style="text-align: center;">
  <button class="button button2">Total {{total}}</button>
  <a href="/customer-address"><button class="button" ><span>Purchase </span></button></a>
</div>


<br><br><br><br><br>
{% endblock content %}
