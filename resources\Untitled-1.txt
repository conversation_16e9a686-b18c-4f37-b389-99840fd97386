{
    'success': True,
    'code': 'PAYMENT_INITIATED',
    'message': 'Payment initiated', 
    'data': {
        'merchantId': 'PGTESTPAYUAT86', 
        'merchantTransactionId': 'MT7850590068188104', 
        'instrumentResponse': {
            'type': 'PAY_PAGE', 
            'redirectInfo': {'
                url': 
                'https://mercury-uat.phonepe.com/transact/simulator?token=2XK78zCpszKErsEv5nYvMj88jxmZii5NSui3JmTcesz6W', 
                'method': 'GET'
            }
        }
    }
}





Received payment data: 

{
    'apiVersion': 2, 
    'apiVersionMinor': 0, 
    'paymentMethodData': {
        'description': 
        'Test Card: Visa\u2006••••\u20061111', 
        'info': {
            'cardDetails': '1111', 
            'cardNetwork': 'VISA'
        }, 
        'tokenizationData': {
            'token': 'examplePaymentMethodToken', 
            'type': 'PAYMENT_GATEWAY'
        }, 
        'type': 'CARD'
    }
}





token:  {'csrfmiddlewaretoken': 'HsHDK5dXvXsVDRwWsPw1MAP7EtcOUXYr9iFCPLtfEeVnL75TfyOiIcnJV7IF5alm', 'paymentData': '{"apiVersion":2,"apiVersionMinor":0,"paymentMethodData":{"description":"US Credit: Visa\u2006••••\u20064242","info":{"cardDetails":"4242","cardNetwork":"VISA"},"tokenizationData":{"token":"{\\n  \\"id\\": \\"tok_1RUffuQQ3USwvpS7p4HQ6D3j\\",\\n  \\"object\\": \\"token\\",\\n  \\"card\\": {\\n    \\"id\\": \\"card_1RUfftQQ3USwvpS7nZ82WK2a\\",\\n    \\"object\\": \\"card\\",\\n    \\"address_city\\": \\"Mountain View\\",\\n    \\"address_country\\": \\"US\\",\\n    \\"address_line1\\": \\"1600 Amphitheatre Parkway\\",\\n    \\"address_line1_check\\": \\"unchecked\\",\\n    \\"address_line2\\": null,\\n    \\"address_state\\": \\"CA\\",\\n    \\"address_zip\\": \\"94043\\",\\n    \\"address_zip_check\\": \\"unchecked\\",\\n    \\"brand\\": \\"Visa\\",\\n    \\"country\\": \\"US\\",\\n    \\"cvc_check\\": null,\\n    \\"dynamic_last4\\": \\"4242\\",\\n    \\"exp_month\\": 12,\\n    \\"exp_year\\": 2027,\\n    \\"funding\\": \\"credit\\",\\n    \\"last4\\": \\"4242\\",\\n    \\"metadata\\": {},\\n    \\"name\\": null,\\n    \\"networks\\": {\\n      \\"preferred\\": null\\n    },\\n    \\"regulated_status\\": \\"unregulated\\",\\n    \\"tokenization_method\\": \\"android_pay\\",\\n    \\"wallet\\": null\\n  },\\n  \\"client_ip\\": \\"**************\\",\\n  \\"created\\": **********,\\n  \\"livemode\\": false,\\n  \\"type\\": \\"card\\",\\n  \\"used\\": false\\n}","type":"PAYMENT_GATEWAY"},"type":"CARD"}}'}



PhonePe payment response data:

    code: ['PAYMENT_SUCCESS']
    merchantId: ['PGTESTPAYUAT86']
    transactionId: ['Tv7KmuQD9XmjwjnqttSb9b']
    amount: ['7500000']
    providerReferenceId: ['T2506020734331549470665']
    param1: ['na']
    checksum: ['2ec420101ce340d500564d384adb5625accf5354bedcfa4588a9a4d915c2dbd0###1']