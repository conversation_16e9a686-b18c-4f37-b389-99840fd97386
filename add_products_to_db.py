import pymysql

connection = pymysql.connect(
    host='localhost',
    user='root',
    password='2004',
    database='onlineshoppingprojectdjango',
    cursorclass=pymysql.cursors.DictCursor
)

cursor = connection.cursor()
path = "C:\\Users\\<USER>\\Downloads\\truncated_amazon_products.csv"

with open(path, "r", encoding="utf-8") as fp:
    data = fp.readlines()

sql_query = '''
    INSERT INTO ecom_product (id, name, product_image, price, description)
    VALUES (%s, %s, %s, %s, %s);
'''

values = []
id = 4

for i in data[1:]:
    temp = i.strip().split(",")
    if len(temp) < 5:
        continue  # skip rows with missing values
    try:
        value = (
            id,
            temp[0].strip(),
            temp[3].strip(),
            int(temp[2]),
            temp[1].strip()
        )
        values.append(value)
        id += 1
    except Exception as e:
        print("Error on row:","->", e)

cursor.executemany(sql_query, values)
connection.commit()  # Don't forget to commit!

cursor.close()
connection.close()
