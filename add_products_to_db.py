import pymysql
import csv
import re

connection = pymysql.connect(
    host='localhost',
    user='root',
    password='2004',
    database='onlineshoppingprojectdjango',
    cursorclass=pymysql.cursors.DictCursor
)

cursor = connection.cursor()
path = "C:\\Users\\<USER>\\Downloads\\truncated_amazon_products.csv"

def extract_price(price_str):
    """Extract numeric price from price string"""
    if not price_str:
        return 0

    # Remove currency symbols and extract numbers
    price_clean = re.sub(r'[^\d.,]', '', str(price_str))
    if not price_clean:
        return 0

    # Handle different decimal separators
    price_clean = price_clean.replace(',', '')

    try:
        # Try to convert to float first, then to int
        price_float = float(price_clean)
        return int(price_float)
    except ValueError:
        return 0

sql_query = '''
    INSERT INTO ecom_product (id, name, product_image, price, description)
    VALUES (%s, %s, %s, %s, %s);
'''

values = []
id = 4
successful_rows = 0
error_rows = 0

try:
    with open(path, "r", encoding="utf-8") as fp:
        # Use CSV reader to handle commas within quoted fields properly
        csv_reader = csv.reader(fp)

        # Skip header row
        next(csv_reader, None)

        for row_num, row in enumerate(csv_reader, start=2):  # start=2 because we skip header
            try:
                # Ensure we have enough columns
                if len(row) < 4:
                    print(f"Row {row_num}: Insufficient columns ({len(row)}), skipping...")
                    error_rows += 1
                    continue

                # Extract data with proper handling
                name = row[0].strip() if len(row) > 0 else "Unknown Product"
                description = row[1].strip() if len(row) > 1 else ""
                price_str = row[2].strip() if len(row) > 2 else "0"
                image_url = row[3].strip() if len(row) > 3 else ""

                # Extract numeric price
                price = extract_price(price_str)

                # Validate data
                if not name or name == "Unknown Product":
                    print(f"Row {row_num}: Empty product name, skipping...")
                    error_rows += 1
                    continue

                # Truncate long fields to fit database constraints
                name = name[:250] if len(name) > 250 else name
                description = description[:250] if len(description) > 250 else description
                image_url = image_url[:250] if len(image_url) > 250 else image_url

                value = (
                    id,
                    name,
                    image_url,
                    price,
                    description
                )
                values.append(value)
                id += 1
                successful_rows += 1

                # Print progress every 100 rows
                if successful_rows % 100 == 0:
                    print(f"Processed {successful_rows} products successfully...")

            except Exception as e:
                error_rows += 1
                print(f"Error on row {row_num}: {str(e)}")
                continue

    print(f"\nProcessing complete!")
    print(f"Successful rows: {successful_rows}")
    print(f"Error rows: {error_rows}")

    if values:
        print(f"Inserting {len(values)} products into database...")
        cursor.executemany(sql_query, values)
        connection.commit()
        print("Database insertion completed successfully!")
    else:
        print("No valid data to insert.")

except FileNotFoundError:
    print(f"Error: CSV file not found at {path}")
    print("Please check the file path and try again.")
except Exception as e:
    print(f"Unexpected error: {str(e)}")
finally:
    cursor.close()
    connection.close()
