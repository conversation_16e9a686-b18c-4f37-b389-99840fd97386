# Generated by Django 5.2.3 on 2025-07-13 09:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ecom', '0016_applyjob_rename_jobs_createjob'),
    ]

    operations = [
        migrations.AlterField(
            model_name='applyjob',
            name='contact_number',
            field=models.Char<PERSON>ield(max_length=15),
        ),
        migrations.AlterField(
            model_name='applyjob',
            name='email',
            field=models.EmailField(max_length=254),
        ),
        migrations.AlterField(
            model_name='applyjob',
            name='gender',
            field=models.CharField(blank=True, choices=[('', 'Gender'), ('M', 'Male'), ('F', 'Female'), ('O', 'Others')], default='', max_length=1),
        ),
        migrations.AlterField(
            model_name='applyjob',
            name='job_type',
            field=models.CharField(choices=[('JV', 'Java Developer'), ('PY', 'Python Developer')], max_length=2),
        ),
        migrations.Alter<PERSON>ield(
            model_name='applyjob',
            name='resume',
            field=models.FileField(upload_to='resumes/'),
        ),
    ]
