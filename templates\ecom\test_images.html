{% extends "ecom/base.html" %}
{% load static %}

{% block content %}
<div class="container" style="margin-top: 100px;">
    <h2 style="text-align: center; color: #333; margin-bottom: 30px;">Image Display Test - Both Local and External URLs</h2>
    
    <div class="row">
        {% for product in products %}
        <div class="col-md-4 mb-4">
            <div class="card" style="height: 100%;">
                <div style="height: 250px; overflow: hidden; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;">
                    {% if product.get_image_url %}
                        <img src="{{ product.get_image_url }}" 
                             alt="{{ product.name }}" 
                             style="max-width: 100%; max-height: 100%; object-fit: contain;"
                             onerror="this.src='{% static 'images/default-product.png' %}'">
                    {% else %}
                        <img src="{% static 'images/default-product.png' %}" 
                             alt="No image available" 
                             style="max-width: 100%; max-height: 100%; object-fit: contain;">
                    {% endif %}
                </div>
                <div class="card-body">
                    <h5 class="card-title">{{ product.name }}</h5>
                    <p class="card-text">{{ product.description|truncatewords:15 }}</p>
                    <p class="card-text"><strong>Price: ${{ product.price }}</strong></p>
                    
                    <!-- Debug info -->
                    <small class="text-muted">
                        <strong>Image Type:</strong> 
                        {% if product.get_image_url %}
                            {% if product.get_image_url|slice:":4" == "http" %}
                                <span class="badge badge-info">External URL</span>
                            {% else %}
                                <span class="badge badge-success">Local File</span>
                            {% endif %}
                        {% else %}
                            <span class="badge badge-warning">No Image</span>
                        {% endif %}
                    </small>
                    <br>
                    <small class="text-muted">
                        <strong>URL:</strong> {{ product.get_image_url|default:"None" }}
                    </small>
                </div>
            </div>
        </div>
        {% if forloop.counter|divisibleby:"3" %}
            </div><div class="row">
        {% endif %}
        {% endfor %}
    </div>
</div>

<style>
.card {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-5px);
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}
