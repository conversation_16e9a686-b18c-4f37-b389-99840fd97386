# Generated by Django 3.0.5 on 2025-06-03 01:36

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('ecom', '0008_errorlogs'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentErrorLogs',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_type', models.CharField(choices=[('stripe', 'Stripe'), ('phonepe', 'PhonePe'), ('google-pay', 'Google Pay'), ('paypal', 'PayPal')], default='unknown', max_length=20)),
                ('error_category', models.CharField(choices=[('INITIATION_FAILED', 'Payment Initiation Failed'), ('PROCESSING_FAILED', 'Payment Processing Failed'), ('VALIDATION_FAILED', 'Payment Validation Failed'), ('CALLBACK_FAILED', 'Payment Callback Failed'), ('ORDER_CREATION_FAILED', 'Order Creation Failed'), ('INSUFFICIENT_DATA', 'Insufficient Customer Data'), ('API_ERROR', 'Payment Gateway API Error'), ('NETWORK_ERROR', 'Network/Connection Error'), ('AUTHENTICATION_ERROR', 'Authentication Error'), ('AMOUNT_MISMATCH', 'Amount Validation Error'), ('TIMEOUT_ERROR', 'Payment Timeout'), ('UNKNOWN_ERROR', 'Unknown Error')], default='UNKNOWN_ERROR', max_length=30)),
                ('error_message', models.TextField(default='No error message provided')),
                ('error_code', models.CharField(blank=True, max_length=50, null=True)),
                ('user_id', models.IntegerField(blank=True, null=True)),
                ('order_id', models.CharField(blank=True, max_length=250, null=True)),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('gateway_transaction_id', models.CharField(blank=True, max_length=250, null=True)),
                ('gateway_response', models.TextField(blank=True, null=True)),
                ('request_method', models.CharField(blank=True, max_length=10, null=True)),
                ('request_url', models.CharField(blank=True, max_length=500, null=True)),
                ('request_data', models.TextField(blank=True, null=True)),
                ('user_agent', models.CharField(blank=True, max_length=500, null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('customer_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('customer_mobile', models.CharField(blank=True, max_length=20, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('resolved', models.BooleanField(default=False)),
                ('resolution_notes', models.TextField(blank=True, null=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Payment Error Log',
                'verbose_name_plural': 'Payment Error Logs',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AlterModelOptions(
            name='errorlogs',
            options={'ordering': ['-timestamp'], 'verbose_name': 'Error Log', 'verbose_name_plural': 'Error Logs'},
        ),
        migrations.AddField(
            model_name='errorlogs',
            name='error_code',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='errorlogs',
            name='error_message',
            field=models.TextField(default='No error message provided'),
        ),
        migrations.AddField(
            model_name='errorlogs',
            name='error_type',
            field=models.CharField(default='UNKNOWN', max_length=50),
        ),
        migrations.AddField(
            model_name='errorlogs',
            name='request_data',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='errorlogs',
            name='resolved',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='errorlogs',
            name='stack_trace',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='errorlogs',
            name='timestamp',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='errorlogs',
            name='user_id',
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
