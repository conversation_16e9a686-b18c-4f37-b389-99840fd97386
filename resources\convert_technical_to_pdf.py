#!/usr/bin/env python3
"""
<PERSON>ript to convert technical documentation HTML to PDF format.
This script uses xhtml2pdf to generate a PDF from the technical HTML documentation.
"""

import os
import sys

def install_xhtml2pdf():
    """Install xhtml2pdf if not available."""
    try:
        from xhtml2pdf import pisa
        return True
    except ImportError:
        print("xhtml2pdf not found. Installing...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "xhtml2pdf"])
            from xhtml2pdf import pisa
            return True
        except Exception as e:
            print(f"Failed to install xhtml2pdf: {e}")
            return False

def convert_html_to_pdf(html_file, pdf_file):
    """Convert HTML file to PDF using xhtml2pdf."""
    try:
        from xhtml2pdf import pisa
        
        # Read HTML content
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Add inline CSS for better PDF formatting
        enhanced_html = html_content.replace(
            '<style>',
            '''<style>
            @page {
                size: A4;
                margin: 1.5cm;
                @bottom-center {
                    content: "Page " counter(page);
                    font-size: 10px;
                    color: #666;
                }
            }
            
            body {
                font-family: Arial, sans-serif;
                line-height: 1.4;
                color: #333;
                font-size: 11px;
            }
            
            .container {
                max-width: none;
                margin: 0;
                padding: 0;
                box-shadow: none;
                background: white;
            }
            
            h1 {
                color: #2c3e50;
                border-bottom: 3px solid #e74c3c;
                padding-bottom: 10px;
                page-break-before: always;
                margin-top: 20px;
                font-size: 24px;
            }
            
            h1:first-of-type {
                page-break-before: auto;
                margin-top: 0;
            }
            
            h2 {
                color: #34495e;
                border-left: 4px solid #e74c3c;
                padding-left: 15px;
                margin-top: 25px;
                page-break-before: always;
                font-size: 18px;
            }
            
            h3 {
                color: #2c3e50;
                margin-top: 20px;
                page-break-after: avoid;
                font-size: 14px;
            }
            
            h4 {
                color: #34495e;
                margin-top: 15px;
                font-size: 12px;
            }
            
            .code-block {
                background-color: #2d3748;
                color: #e2e8f0;
                padding: 12px;
                margin: 10px 0;
                font-family: Courier New, monospace;
                font-size: 9px;
                page-break-inside: avoid;
                white-space: pre-wrap;
                border-radius: 4px;
            }
            
            .code-inline {
                background-color: #f1f5f9;
                color: #1e293b;
                padding: 2px 4px;
                font-family: Courier New, monospace;
                font-size: 9px;
            }
            
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 10px 0;
                page-break-inside: avoid;
                font-size: 9px;
            }
            
            th, td {
                border: 1px solid #e5e7eb;
                padding: 6px;
                text-align: left;
            }
            
            th {
                background-color: #f3f4f6;
                font-weight: bold;
                color: #374151;
            }
            
            .toc {
                page-break-after: always;
                background-color: #f8fafc;
                padding: 15px;
                border-radius: 5px;
                border: 1px solid #e2e8f0;
            }
            
            .concept-box, .implementation-box, .highlight, .warning {
                page-break-inside: avoid;
                margin: 10px 0;
                padding: 12px;
                border-radius: 4px;
            }
            
            .concept-box {
                background-color: #eff6ff;
                border: 2px solid #3b82f6;
            }
            
            .implementation-box {
                background-color: #f0fdf4;
                border: 2px solid #22c55e;
            }
            
            .highlight {
                background-color: #fef3c7;
                border-left: 4px solid #f59e0b;
            }
            
            .warning {
                background-color: #fef2f2;
                border: 2px solid #ef4444;
                color: #721c24;
            }
            
            .flow-diagram {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                padding: 15px;
                margin: 10px 0;
                text-align: center;
            }
            
            ul, ol {
                margin: 5px 0;
                padding-left: 18px;
            }
            
            li {
                margin: 2px 0;
            }
            
            p {
                margin: 6px 0;
                text-align: justify;
            }
            
            code {
                background-color: #f1f1f1;
                padding: 2px 4px;
                font-family: Courier New, monospace;
                font-size: 9px;
            }
            '''
        )
        
        # Create PDF
        result_file = open(pdf_file, "w+b")
        pisa_status = pisa.CreatePDF(enhanced_html, dest=result_file)
        result_file.close()
        
        if pisa_status.err:
            print(f"Error creating PDF: {pisa_status.err}")
            return False
        else:
            print(f"PDF successfully created: {pdf_file}")
            return True
        
    except Exception as e:
        print(f"Error converting to PDF: {e}")
        return False

def main():
    """Main function to convert technical documentation to PDF."""
    # File paths
    html_file = "technical_documentation.html"
    pdf_file = "Django_E-Commerce_Technical_Implementation_Guide.pdf"
    
    # Check if HTML file exists
    if not os.path.exists(html_file):
        print(f"Error: {html_file} not found!")
        return False
    
    # Install xhtml2pdf if needed
    if not install_xhtml2pdf():
        print("Failed to install xhtml2pdf. Please install it manually:")
        print("pip install xhtml2pdf")
        return False
    
    # Convert to PDF
    print(f"Converting {html_file} to {pdf_file}...")
    success = convert_html_to_pdf(html_file, pdf_file)
    
    if success:
        print(f"\n✅ Technical documentation successfully converted to PDF!")
        print(f"📄 File location: {os.path.abspath(pdf_file)}")
        print(f"📊 File size: {os.path.getsize(pdf_file) / 1024:.1f} KB")
        print(f"\n📚 This technical guide covers:")
        print("   • Django MVT Architecture Implementation")
        print("   • Models & ORM with detailed code examples")
        print("   • Views & Business Logic patterns")
        print("   • Templates & Frontend Integration")
        print("   • Forms & Data Validation")
        print("   • Authentication & Authorization")
        print("   • Session Management & Cookies")
        print("   • File Upload & Static Files")
        print("   • Email Integration")
        print("   • Payment Gateway Integration")
        print("   • Admin Interface Customization")
        print("   • Security Implementation")
        print("   • Performance Optimization")
    else:
        print("\n❌ Failed to convert technical documentation to PDF")
    
    return success

if __name__ == "__main__":
    main()
