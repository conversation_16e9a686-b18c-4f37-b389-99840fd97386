# Generated by Django 5.2.3 on 2025-07-23 15:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ecom', '0042_remove_resumelogs_resume_attribute_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ResumeAttributes',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('analysis_json', models.TextField()),
                ('custom_attributes', models.TextField()),
                ('analysis_success', models.BooleanField()),
                ('created_by', models.CharField(max_length=50)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('updated_date', models.DateTimeField(auto_now=True)),
                ('apply_resume', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resume_attributes', to='ecom.applyjob')),
            ],
        ),
        migrations.CreateModel(
            name='ResumeLogs',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('custom_attributes', models.TextField()),
                ('created_by', models.CharField(max_length=50)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('apply_resume', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resume_logs', to='ecom.applyjob')),
                ('resume_attribute', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attribute_logs', to='ecom.resumeattributes')),
            ],
        ),
    ]
