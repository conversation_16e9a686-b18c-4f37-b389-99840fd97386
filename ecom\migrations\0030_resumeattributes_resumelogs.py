# Generated by Django 5.2.3 on 2025-07-22 01:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ecom', '0029_remove_applyjob_custom_attributes'),
    ]

    operations = [
        migrations.CreateModel(
            name='ResumeAttributes',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filter_name', models.CharField(max_length=100)),
                ('filter_value', models.CharField(max_length=150)),
                ('created_by', models.CharField(max_length=50)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('updated_date', models.DateTimeField(auto_now=True)),
                ('apply_resume', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resume_attributes', to='ecom.applyjob')),
            ],
        ),
        migrations.CreateModel(
            name='ResumeLogs',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filter_name', models.CharField(max_length=100)),
                ('filter_value', models.CharField(max_length=150)),
                ('created_by', models.CharField(max_length=50)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('apply_resume', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resume_logs', to='ecom.applyjob')),
                ('resume_attribute', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attribute_logs', to='ecom.resumeattributes')),
            ],
        ),
    ]
