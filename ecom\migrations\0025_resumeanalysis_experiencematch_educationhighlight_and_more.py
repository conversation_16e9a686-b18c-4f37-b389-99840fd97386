# Generated by Django 5.2.3 on 2025-07-19 13:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ecom', '0024_applyjob_custom_attributes'),
    ]

    operations = [
        migrations.CreateModel(
            name='ResumeAnalysis',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('candidate_name', models.CharField(max_length=150)),
                ('email', models.EmailField(max_length=254)),
                ('contact_number', models.CharField(max_length=15)),
                ('final_score', models.FloatField()),
                ('skills_match', models.FloatField()),
                ('experience_score', models.FloatField()),
                ('education_score', models.FloatField()),
                ('keywords_match', models.FloatField()),
                ('overall_fit', models.FloatField()),
                ('growth_potential', models.Float<PERSON>ield()),
                ('recommendation_decision', models.CharField(max_length=20)),
                ('recommendation_reason', models.TextField()),
                ('recommendation_confidence', models.CharField(max_length=20)),
                ('skill_match_percentage', models.FloatField()),
                ('experience_level', models.CharField(max_length=20)),
                ('education_level', models.CharField(max_length=20)),
                ('is_fresher', models.BooleanField(default=True)),
                ('first_job_start_year', models.IntegerField(blank=True, null=True)),
                ('last_job_end_year', models.IntegerField(blank=True, null=True)),
                ('total_jobs_count', models.IntegerField(default=0)),
                ('average_job_change', models.FloatField(blank=True, null=True)),
                ('salary_expectation_alignment', models.CharField(max_length=20)),
                ('onboarding_priority', models.CharField(max_length=20)),
                ('processing_time', models.FloatField()),
                ('processed_at', models.DateTimeField()),
                ('file_path', models.TextField()),
                ('file_size', models.BigIntegerField()),
                ('word_count', models.IntegerField()),
                ('success', models.BooleanField(default=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('application', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analysis', to='ecom.applyjob')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ExperienceMatch',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('experience_text', models.TextField()),
                ('is_matching', models.BooleanField()),
                ('analysis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='experiences', to='ecom.resumeanalysis')),
            ],
        ),
        migrations.CreateModel(
            name='EducationHighlight',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('education_text', models.TextField()),
                ('analysis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='education_highlights', to='ecom.resumeanalysis')),
            ],
        ),
        migrations.CreateModel(
            name='Assessment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assessment_type', models.CharField(choices=[('STRENGTH', 'Strength'), ('WEAKNESS', 'Weakness'), ('RED_FLAG', 'Red Flag'), ('CULTURAL_FIT', 'Cultural Fit Indicator')], max_length=20)),
                ('assessment_text', models.TextField()),
                ('analysis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessments', to='ecom.resumeanalysis')),
            ],
        ),
        migrations.CreateModel(
            name='InterviewFocus',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('focus_area', models.CharField(max_length=200)),
                ('analysis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interview_focuses', to='ecom.resumeanalysis')),
            ],
            options={
                'unique_together': {('analysis', 'focus_area')},
            },
        ),
        migrations.CreateModel(
            name='SkillMatch',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('skill_name', models.CharField(max_length=100)),
                ('is_matching', models.BooleanField()),
                ('analysis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='skills', to='ecom.resumeanalysis')),
            ],
            options={
                'unique_together': {('analysis', 'skill_name', 'is_matching')},
            },
        ),
    ]
