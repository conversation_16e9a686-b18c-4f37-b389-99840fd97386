# Generated manually for single table resume analysis
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ecom', '0025_resumeanalysis_experiencematch_educationhighlight_and_more'),
    ]

    operations = [
        # Add JSON fields to ResumeAnalysis
        migrations.AddField(
            model_name='resumeanalysis',
            name='matching_skills',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='resumeanalysis',
            name='missing_skills',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='resumeanalysis',
            name='matching_experience',
            field=models.J<PERSON><PERSON>ield(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='resumeanalysis',
            name='experience_gaps',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='resumeanalysis',
            name='education_highlights',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.Add<PERSON>ield(
            model_name='resumeanalysis',
            name='strengths',
            field=models.JSO<PERSON>ield(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='resumeanalysis',
            name='weaknesses',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='resumeanalysis',
            name='red_flags',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='resumeanalysis',
            name='cultural_fit_indicators',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='resumeanalysis',
            name='interview_focus_areas',
            field=models.JSONField(blank=True, default=list),
        ),
    ]
