import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ecommerce.settings')
django.setup()

from ecom.models import Product

def verify_products():
    """Verify that products were added successfully"""
    total_products = Product.objects.count()
    print(f"Total products in database: {total_products}")
    
    print("\nRecently added products (last 10):")
    recent_products = Product.objects.all().order_by('-id')[:10]
    
    for product in recent_products:
        print(f"ID: {product.id}")
        print(f"Name: {product.name}")
        print(f"Price: ${product.price}")
        print(f"Description: {product.description[:100]}...")
        print(f"Image URL: {product.product_image}")
        print("-" * 50)

if __name__ == "__main__":
    verify_products()
