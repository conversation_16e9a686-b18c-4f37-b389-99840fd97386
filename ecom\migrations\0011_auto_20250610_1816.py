# Generated by Django 3.0.5 on 2025-06-10 12:46

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('ecom', '0010_auto_20250603_0715'),
    ]

    operations = [
        migrations.RenameField(
            model_name='phonepelogs',
            old_name='transactionId',
            new_name='phonepeTransactionId',
        ),
        migrations.AddField(
            model_name='googlepaylogs',
            name='transaction_id',
            field=models.CharField(default=django.utils.timezone.now, max_length=250),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='paymenterrorlogs',
            name='transaction_id',
            field=models.CharField(default=django.utils.timezone.now, max_length=250),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='paymentlogs',
            name='transaction_id',
            field=models.Char<PERSON>ield(default=django.utils.timezone.now, max_length=250),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='phonepelogs',
            name='transaction_id',
            field=models.CharField(default=django.utils.timezone.now, max_length=250),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='stripelogs',
            name='transaction_id',
            field=models.CharField(default=django.utils.timezone.now, max_length=250),
            preserve_default=False,
        ),
    ]
