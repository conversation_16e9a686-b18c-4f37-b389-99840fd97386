{% extends 'ecom/admin_base.html' %}
{% load static %}
{% block content %}
<br>
{%include 'ecom/admin_dashboard_cards.html'%}
<br><br><br><br><br>

<hr><hr>
<div class="container" >

    <div class="panel panel-primary" style="border-radius: 12px; box-shadow: 0 8px 32px rgba(26, 35, 126, 0.15); border: none;">
      <div class="panel-heading" style="text-align:center; background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%); border-radius: 12px 12px 0 0; color: white;">
        <h6 class="panel-title" style="font-weight: 600; font-size: 18px;">Recent Orders</h6>
      </div>
      <table class="table table-hover" id="dev-table">
        <thead>
          <tr>
            <th>Customer Name</th>
            <th>Customer Mobile</th>
            <th>Shipment Address</th>
            <th>Product Name</th>
            <th>Product Picture</th>
            <th>Status</th>

          </tr>
        </thead>
        {% for product,customer,order in data %}
        <tr>
          {% for c in customer %}
          <td> {{c.get_name}}</td>
          <td>{{c.mobile}}</td>
          <td>{{order.address}}</td>

          {% endfor %}
          {% for p in product %}
          <td> {{p.name}}</td>
          <td>
            {% if p.get_image_url %}
            <img src="{{ p.get_image_url }}" alt="Product Image" height="40px" width="40px" />
            {% else %}
            <img src="{% static 'images/default-product.png' %}" alt="Product Image" height="40px" width="40px" />
            {% endif %}
          </td>

          {% if order.status == 'Pending'%}
          <td> <span class="label label-info">{{order.status}}</span></td>
          {% elif order.status == 'Delivered' %}
          <td> <span class="label label-success">{{order.status}}</span></td>
          {% elif order.status == 'Out for Delivery' %}
          <td> <span class="label label-warning">{{order.status}}</span></td>
          {%else%}
            <td> <span class="label label-primary">{{order.status}}</span></td>
          {%endif%}
          {% endfor %}
        </tr>
        {% endfor %}





      </table>

  </div>
</div>


{% endblock content %}
