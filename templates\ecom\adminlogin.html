<!DOCTYPE html>
{% load widget_tweaks %}
<html lang="en" dir="ltr">
  <head>
    <meta charset="utf-8">
    <title></title>
    <style media="screen">
      body {
    margin: 0;
    padding: 0;
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
    min-height: 100vh;
}

.box {
    width: 500px;
    padding: 40px;
    position: absolute;
    top: 40%;
    left: 50%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    text-align: center;
    transition: all 0.3s ease;
    margin-top: 100px;
    border-radius: 16px;
    box-shadow: 0 16px 48px rgba(26, 35, 126, 0.2);
    transform: translate(-50%, -50%);
}

.box input[type="text"],
.box input[type="password"] {
    border: 0;
    background: rgba(255, 255, 255, 0.9);
    display: block;
    margin: 20px auto;
    text-align: center;
    border: 2px solid #ff7043;
    padding: 12px 16px;
    width: 250px;
    outline: none;
    color: #212121;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-size: 16px;
}

.box h1 {
    color: #1a237e;
    text-transform: uppercase;
    font-weight: 700;
    margin-bottom: 10px;
}

.text-muted {
    color: #666666 !important;
    font-size: 14px;
}

.box input[type="text"]:focus,
.box input[type="password"]:focus {
    width: 300px;
    border-color: #1a237e;
    box-shadow: 0 0 0 3px rgba(26, 35, 126, 0.1);
    background: rgba(255, 255, 255, 1);
}

.box input[type="submit"] {
    border: 0;
    background: linear-gradient(135deg, #ff7043 0%, #ff8a65 100%);
    display: block;
    margin: 20px auto;
    text-align: center;
    padding: 14px 40px;
    outline: none;
    color: white;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-weight: 600;
    font-size: 16px;
    box-shadow: 0 4px 16px rgba(255, 112, 67, 0.3);
}

.box input[type="submit"]:hover {
    background: linear-gradient(135deg, #ff8a65 0%, #ffab91 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 112, 67, 0.4);
}


    </style>
  </head>
  {% comment %} <body>
    {% include "ecom/navbar.html" %}
    <div class="container">
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <form class="box" method="post">
                    {% csrf_token %}
                    <h1>Admin Login</h1>
                    <p class="text-muted"> Please enter your login and password!</p>

                     {% render_field form.username class="form-control" placeholder="Username" %}
                     {% render_field form.password class="form-control" placeholder="Password" %}

                     <input type="submit" name="" value="Login">

                </form>
            </div>
        </div>
    </div>
</div>
<br><br><br><br><br><br><br><br><br>
<br><br><br><br><br><br><br><br><br>
<br><br><br><br><br><br><br><br><br>
{% include "ecom/footer.html" %}
  </body> {% endcomment %}
  <body>
    {% include "ecom/navbar.html" %}

    <form class="box" method="post">
      {% csrf_token %}
      <h1>Admin Login</h1>
      <p class="text-muted"> Please enter your login and password!</p>

      {% render_field form.username class="form-control" placeholder="Username" %}
      {% render_field form.password class="form-control" placeholder="Password" %}
      <input type="submit" value="Login">
    </form>
    <br><br><br><br><br><br><br><br><br>
<br><br><br><br><br><br><br><br><br>
<br><br><br><br><br><br><br><br><br>
    {% include "ecom/footer.html" %}
  </body>
</html>
