
import pymysql

# Establish connection
connection = pymysql.connect(
    host='localhost',       # or your DB host
    user='root',   # your MySQL username
    password='2004', # your MySQL password
    database='onlineshoppingprojectdjango', # name of the database
    cursorclass=pymysql.cursors.DictCursor  # returns rows as dictionaries
)

# Create a cursor
cursor = connection.cursor()

# Execute a query
cursor.execute("SELECT * FROM ecom_product;")

# Fetch result
version = cursor.fetchall()
print("Database version:", version)

# Close connection
cursor.close()
connection.close()