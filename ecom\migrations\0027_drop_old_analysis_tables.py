# Drop old analysis tables that are no longer needed
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('ecom', '0026_single_table_resume_analysis'),
    ]

    operations = [
        # Drop old tables (if they exist)
        migrations.RunSQL(
            "DROP TABLE IF EXISTS ecom_skillmatch;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS ecom_experiencematch;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS ecom_educationhighlight;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS ecom_assessment;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS ecom_interviewfocus;",
            reverse_sql="-- Cannot reverse this operation"
        ),
    ]
