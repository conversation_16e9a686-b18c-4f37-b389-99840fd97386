{% extends 'ecom/admin_base.html' %}
{% load static %}
{% block content %}


<head>
  <link href="//netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap.min.css" rel="stylesheet" id="bootstrap-css">
  <script src="//netdna.bootstrapcdn.com/bootstrap/3.0.0/js/bootstrap.min.js"></script>
  <script src="//code.jquery.com/jquery-1.11.1.min.js"></script>

  <style media="screen">
    a:link {
      text-decoration: none;
    }

    h6 {
      text-align: center;
    }

    .row {
      margin: 100px;
    }

    .float{
	position:fixed;
	width:70px;
	height:70px;
	bottom:60px;
	right:60px;
	background-color:red;
	color:#FFF;
	border-radius:50px;
	text-align:center;
	box-shadow: 3px 3px 3px #999;
}

.my-float{
	margin-top:22px;
}
  </style>
</head>

<div class="container">
  <div class="panel panel-primary">
    <div class="panel-heading">
      <h6 class="panel-title">Total Product List</h6>
    </div>
    <table class="table table-hover" id="dev-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Image</th>
          <th>Price</th>
          <th>Description</th>
          <th>Update</th>
          <th>Delete</th>
        </tr>
      </thead>
      {% for p in products %}
  {% if p %}

      <tr>
        <td> {{p.name}}</td>
        <td>
          {% if p.get_image_url %}
          <img src="{{ p.get_image_url }}" alt="Product Image" height="40px" width="40px" />
          {% else %}
          <img src="{% static 'images/default-product.png' %}" alt="Product Image" height="40px" width="40px" />
          {% endif %}
        </td>
        <td>{{p.price}}</td>
        <td>{{p.description}}</td>
        <td><a class="btn btn-primary btn-xs" href="{% url 'update-product' p.id  %}"><span class="glyphicon glyphicon-edit"></span></a></td>
        <td><a class="btn btn-danger btn-xs" href="{% url 'delete-product' p.id  %}"><span class="glyphicon glyphicon-trash"></span></a></td>

      </tr>
      {%else%}
      <br><br><br>
      <h1>no products</h1>
      {% endif %}
      {% endfor %}
    </table>
  </div>
</div>
<!-- add product floating button -->
<a href="/admin-add-product" class="float">
<i class="fa fa-plus my-float"></i>
</a>
{% endblock content %}
