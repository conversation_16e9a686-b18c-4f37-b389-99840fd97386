# Phonepe response

# Payment Success - POST params: <QueryDict: {'code': ['PAYMENT_SUCCESS'], 'merchantId': ['PGTESTPAYUAT86'], 'transactionId': ['7wqZNzUTF7mjNDANaEuHYq'], 'amount': ['25000000'], 'providerReferenceId': ['T2506020822514329470681'], 'param1': ['na'], 'param2': ['na'], 'param3': ['na'], 'param4': ['na'], 'param5': ['na'], 'param6': ['na'], 'param7': ['na'], 'param8': ['na'], 'param9': ['na'], 'param10': ['na'], 'param11': ['na'], 'param12': ['na'], 'param13': ['na'], 'param14': ['na'], 'param15': ['na'], 'param16': ['na'], 'param17': ['na'], 'param18': ['na'], 'param19': ['na'], 'param20': ['na'], 'checksum': ['2ec420101ce340d500564d384adb5625accf5354bedcfa4588a9a4d915c2dbd0###1']}>

import jsons
from pprint import pprint

data =  {
    'apiVersion': 2, 
    'apiVersionMinor': 0, 
    'paymentMethodData': {
        'description': 'US Credit: Visa\u2006••••\u20064242', 
        'info': {
            'cardDetails': '4242', 
            'cardNetwork': 'VISA'
            }, 
        'tokenizationData': {
            'token': '{\n  "id": "tok_1RVObUQQ3USwvpS7j6N1eZ0T",\n  "object": "token",\n  "card": {\n    "id": "card_1RVObUQQ3USwvpS7T5C3vFgA",\n    "object": "card",\n    "address_city": "Mountain View",\n    "address_country": "US",\n    "address_line1": "1600 Amphitheatre Parkway",\n    "address_line1_check": "unchecked",\n    "address_line2": null,\n    "address_state": "CA",\n    "address_zip": "94043",\n    "address_zip_check": "unchecked",\n    "brand": "Visa",\n    "country": "US",\n    "cvc_check": null,\n    "dynamic_last4": "4242",\n    "exp_month": 12,\n    "exp_year": 2027,\n    "funding": "credit",\n    "last4": "4242",\n    "metadata": {},\n    "name": null,\n    "networks": {\n      "preferred": null\n    },\n    "regulated_status": "unregulated",\n    "tokenization_method": "android_pay",\n    "wallet": null\n  },\n  "client_ip": "*************",\n  "created": **********,\n  "livemode": false,\n  "type": "card",\n  "used": false\n}', 
            'type': 'PAYMENT_GATEWAY'}, 
        'type': 'CARD'
        }
    }

pprint(jsons.loads(data['paymentMethodData']['tokenizationData']['token']))

{'card': {'address_city': 'Mountain View',
          'address_country': 'US',
          'address_line1': '1600 Amphitheatre Parkway',
          'address_line1_check': 'unchecked',
          'address_line2': None,
          'address_state': 'CA',
          'address_zip': '94043',
          'address_zip_check': 'unchecked',
          'brand': 'Visa',
          'country': 'US',
          'cvc_check': None,
          'dynamic_last4': '4242',
          'exp_month': 12,
          'exp_year': 2027,
          'funding': 'credit',
          'id': 'card_1RVObUQQ3USwvpS7T5C3vFgA',
          'last4': '4242',
          'metadata': {},
          'name': None,
          'networks': {'preferred': None},
          'object': 'card',
          'regulated_status': 'unregulated',
          'tokenization_method': 'android_pay',
          'wallet': None},
 'client_ip': '*************',
 'created': **********,
 'id': 'tok_1RVObUQQ3USwvpS7j6N1eZ0T',
 'livemode': False,
 'object': 'token',
 'type': 'card',
 'used': False}



<QueryDict: {
    'code': ['PAYMENT_SUCCESS'], 
    'merchantId': ['PGTESTPAYUAT86'], 
    'transactionId': ['7wqZNzUTF7mjNDANaEuHYq'], 
    'amount': ['25000000'], 
    'providerReferenceId': ['T2506020822514329470681'], 
    'checksum': ['2ec420101ce340d500564d384adb5625accf5354bedcfa4588a9a4d915c2dbd0###1']
    }>
