# Generated by Django 3.0.5 on 2025-06-02 06:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ecom', '0006_googlepaylogs'),
    ]

    operations = [
        migrations.CreateModel(
            name='PhonepeLogs',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('merchantId', models.CharField(max_length=250)),
                ('payment_status', models.CharField(max_length=250)),
                ('transactionId', models.CharField(max_length=250)),
                ('providerReferenceId', models.CharField(max_length=250)),
                ('checksum', models.<PERSON><PERSON><PERSON><PERSON>(max_length=250)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
            ],
        ),
    ]
