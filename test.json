[{"success": true, "filename": "<PERSON><PERSON><PERSON>_<PERSON>_Resume.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "contact_number": "09876543217", "scores": {"final_score": 78.13, "skills_match": 66.**************, "experience_score": 95.0, "education_score": 100, "keywords_match": 74.33333333333333, "overall_fit": 93.0, "growth_potential": 80.0}, "recommendation": {"decision": "CONSIDER", "reason": "Strong technical background but limited experience as a fresher and no recent experience with Git or problem-solving and algorithmic thinking", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": ["Senior Software Engineer at Microsoft, Hyderabad", "Software Engineer at Google, Bangalore"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Master of Technology in Software Engineering from IIT Madras (2016) with CGPA: 9.0/10"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": false, "first_job_start_year": 2016, "last_job_end_year": 2025, "total_jobs_count": 3, "average_job_change": "3 years"}, "assessment": {"strengths": ["Technical leadership", "Team management", "Scalable backend systems development"], "weaknesses": ["Limited experience as a fresher, no recent experience with Git or problem-solving and algorithmic thinking"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills for a fresher", "Problem-solving and algorithmic thinking"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 34.08153438568115, "processed_at": "2025-07-29T10:27:46.050193", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\Anitha_Reddy_Resume.pdf", "file_size": 0, "word_count": 152, "success": true, "error": null}, "summary": "Experienced engineering manager with 8+ years of experience in software development and team management, now focusing on technical leadership. Strong technical background in Python and database systems."}, {"success": true, "filename": "<PERSON><PERSON><PERSON>_<PERSON>_Resume.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "contact_number": "09876543210", "scores": {"final_score": 92.35, "skills_match": 100, "experience_score": 75.0, "education_score": 90.0, "keywords_match": 93.33333333333333, "overall_fit": 87.0, "growth_potential": 80.0}, "recommendation": {"decision": "HIRE", "reason": "Strong technical skills, good problem-solving skills, and eagerness to learn.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "Git"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["Python Development Intern at TechStart Solutions"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Bachelor of Technology in Computer Science from Vignan University, Guntur (2024) CGPA: 8.5/10"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": true, "first_job_start_year": 0, "last_job_end_year": 0, "total_jobs_count": 0, "average_job_change": "No job changes"}, "assessment": {"strengths": ["Strong foundation in Python programming and MySQL database management", "Good problem-solving skills"], "weaknesses": ["Limited experience as an intern", "No full-time work experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Backend development using Python and MySQL", "Problem-solving and algorithmic thinking"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 25.565655946731567, "processed_at": "2025-07-29T10:28:11.664412", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\<PERSON><PERSON><PERSON>_Reddy_Resume.pdf", "file_size": 0, "word_count": 135, "success": true, "error": null}, "summary": "Passionate Computer Science graduate with strong foundation in Python programming and MySQL database management. Eager to contribute to backend development projects and learn from experienced developers."}, {"success": true, "filename": "resume_sasidhar.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "contact_number": "08555029262", "scores": {"final_score": 95.05, "skills_match": 100, "experience_score": 85.0, "education_score": 85.0, "keywords_match": 98.33333333333333, "overall_fit": 96.0, "growth_potential": 80.0}, "recommendation": {"decision": "HIRE", "reason": "Strong technical skills, relevant experience, and good education background.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "Git"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["Internship at SwechaAP", "Internship at Supraja Technoligies"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor of Technology in Computer Science and Engineering"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": true, "first_job_start_year": 0, "last_job_end_year": 0, "total_jobs_count": 0, "average_job_change": "No job changes"}, "assessment": {"strengths": ["Detail-oriented developer with hands-on experience designing and deploying Python-based web applications using Django.", "Proven expertise in backend development, database integration with MySQL, and crafting dynamic user interfaces using HTML and JavaScript."], "weaknesses": ["Limited experience in algorithmic thinking and problem-solving skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Algorithmic thinking", "Problem-solving skills"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 28.094091653823853, "processed_at": "2025-07-29T10:28:39.867359", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\resume_sasidhar.pdf", "file_size": 0, "word_count": 578, "success": true, "error": null}, "summary": "Detail-oriented developer with hands-on experience designing and deploying Python-based web applications using Django. Proven expertise in backend development, database integration with MySQL, and crafting dynamic user interfaces using HTML and JavaScript."}, {"success": true, "filename": "<PERSON><PERSON>_<PERSON>_Resume.pdf", "candidate_name": "<PERSON><PERSON>", "email": "<EMAIL>", "contact_number": "09123456789", "scores": {"final_score": 87.05, "skills_match": 100, "experience_score": 65.0, "education_score": 100, "keywords_match": 68.0, "overall_fit": 77.0, "growth_potential": 80.0}, "recommendation": {"decision": "HIRE", "reason": "Candidate has relevant technical skills, but lacks direct experience in Python backend development and data-driven applications.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "Git"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["Senior Financial Analyst at HDFC Bank", "Financial Analyst at ICICI Bank"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["MBA in Finance from IIM Indore (2014)", "Bachelor of Commerce from Delhi University (2012)"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": false, "first_job_start_year": 2014, "last_job_end_year": 2025, "total_jobs_count": 2, "average_job_change": "5 years 6 months"}, "assessment": {"strengths": ["Strong analytical skills", "Recent Python certification", "Domain expertise in finance"], "weaknesses": ["Limited experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Backend development", "Data-driven applications", "Python fundamentals"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 31.**************, "processed_at": "2025-07-29T10:29:11.689851", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\Deepak_Sharma_Resume.pdf", "file_size": 0, "word_count": 163, "success": true, "error": null}, "summary": "Senior Financial Analyst with 10+ years of experience in banking sector, transitioning to Python development. Strong analytical skills and recent Python certification."}, {"success": true, "filename": "<PERSON>_Kumar_Resume.pdf", "candidate_name": "<PERSON>", "email": "<EMAIL>", "contact_number": "***********", "scores": {"final_score": 73.43, "skills_match": 66.**************, "experience_score": 75.0, "education_score": 100, "keywords_match": 71.**************, "overall_fit": 87.0, "growth_potential": 80.0}, "recommendation": {"decision": "CONSIDER", "reason": "Strong technical skills and relevant experience make <PERSON> a good fit for the role.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": ["Full Stack Developer Intern at Tech Innovations Pvt Ltd"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Bachelor of Technology in Information Technology from VIT-AP University with CGPA: 9.1/10"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": true, "first_job_start_year": 0, "last_job_end_year": 0, "total_jobs_count": 0, "average_job_change": "No job changes"}, "assessment": {"strengths": ["Extensive knowledge in multiple programming languages and frameworks", "AWS Certified Developer", "Google Cloud Professional", "Hackathon Winner 2023"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Friendly, collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Problem-solving and algorithmic thinking", "Collaboration with frontend developers"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 25.89488124847412, "processed_at": "2025-07-29T10:29:37.632095", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\Kiran_Kumar_Resume.pdf", "file_size": 0, "word_count": 147, "success": true, "error": null}, "summary": "Highly skilled fresh graduate with extensive knowledge in multiple programming languages and frameworks, strong technical skills, and relevant experience."}, {"success": true, "filename": "python-developer2-test.pdf", "candidate_name": "<PERSON>", "email": "<EMAIL>", "contact_number": "441234567890", "scores": {"final_score": 88.65, "skills_match": 100, "experience_score": 90.0, "education_score": 55.0, "keywords_match": 74.33333333333333, "overall_fit": 80.0, "growth_potential": 80.0}, "recommendation": {"decision": "HIRE", "reason": "Strong technical skills, but limited experience as a fresher and no direct experience with backend development and data-driven applications.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "GitLab (implies Git)"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["Created the back-end financial systems that made 20+ RW applications user-friendly and seamless to navigate.", "Developed and updated productivity applications, which increased user downloads by 30 within 96 hours of release."], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Associate in Applied Science Computer Science"], "education_level": "BASIC"}, "job_analysis": {"fresher": false, "first_job_start_year": 2018, "last_job_end_year": 2025, "total_jobs_count": 3, "average_job_change": "2 years 4 months"}, "assessment": {"strengths": ["Strong Python skills", "Experience with MySQL databases", "Version control systems like Git"], "weaknesses": ["Limited experience as a fresher", "No direct experience with backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Backend development", "Data-driven applications"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 47.90688443183899, "processed_at": "2025-07-29T10:30:25.665268", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\python-developer2-test.pdf", "file_size": 0, "word_count": 380, "success": true, "error": null}, "summary": "<PERSON> is a Python developer with strong technical skills, including experience with MySQL databases and version control systems like Git. However, he has limited experience as a fresher and no direct experience with backend development and data-driven applications."}, {"success": true, "filename": "python-developer1-test.pdf", "candidate_name": "<PERSON>", "email": "<EMAIL>", "contact_number": "1234456789", "scores": {"final_score": 91.0, "skills_match": 100, "experience_score": 95.0, "education_score": 55.0, "keywords_match": 79.**************, "overall_fit": 91.0, "growth_potential": 80.0}, "recommendation": {"decision": "HIRE", "reason": "Strong technical skills, but limited experience in full-stack development roles and no formal education beyond Associate's degree.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "Git"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["Junior <PERSON>eloper at Resume Worded, New York, NY 09/2015 - Present"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Associate of Applied Science in Computer Programming from Resume Worded University, New York, NY 06/2005"], "education_level": "BASIC"}, "job_analysis": {"fresher": false, "first_job_start_year": 2005, "last_job_end_year": 2025, "total_jobs_count": 5, "average_job_change": "4 years"}, "assessment": {"strengths": ["Problem-solving and algorithmic thinking", "Collaboration with frontend developers", "Debugging and optimizing backend code"], "weaknesses": ["Limited experience in full-stack development roles", "No formal education mentioned beyond Associate's degree"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Problem-solving and algorithmic thinking", "Full-stack development experience"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 47.066930294036865, "processed_at": "2025-07-29T10:31:12.948878", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\python-developer1-test.pdf", "file_size": 0, "word_count": 331, "success": true, "error": null}, "summary": "Junior Python Developer with 5+ years of experience in backend development, data structures, and MySQL queries. Strong problem-solving and algorithmic thinking skills."}, {"success": true, "filename": "<PERSON>_<PERSON>_Resume.pdf", "candidate_name": "<PERSON>", "email": "ravi.k<PERSON><EMAIL>", "contact_number": "09876012345", "scores": {"final_score": 92.8, "skills_match": 100, "experience_score": 95.0, "education_score": 85.0, "keywords_match": 73.0, "overall_fit": 87.0, "growth_potential": 80.0}, "recommendation": {"decision": "HIRE", "reason": "Strong technical skills, relevant experience, and good education background.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "Git"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["Freelance Python Developer", "Python Developer at Zoho Corporation"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor of Engineering in Computer Science from Anna University, Chennai (2019)"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": false, "first_job_start_year": 2019, "last_job_end_year": 2025, "total_jobs_count": 2, "average_job_change": "3 years"}, "assessment": {"strengths": ["Expertise in rapid prototyping and agile development", "Delivered 25+ projects for various clients including startups and SMEs"], "weaknesses": ["Limited experience with Git and AWS"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Problem-solving and algorithmic thinking", "Experience with Git and AWS"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 32.873544216156006, "processed_at": "2025-07-29T10:31:45.943686", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\Ravi_Krishnan_Resume.pdf", "file_size": 0, "word_count": 162, "success": true, "error": null}, "summary": "<PERSON> is a freelance Python developer with 5+ years of experience delivering custom solutions for various clients. He has expertise in rapid prototyping and agile development."}, {"success": true, "filename": "<PERSON><PERSON>_Patel_Resume.pdf", "candidate_name": "<PERSON><PERSON>", "email": "<EMAIL>", "contact_number": "08765432109", "scores": {"final_score": 62.06, "skills_match": 66.**************, "experience_score": 50.0, "education_score": 85.0, "keywords_match": 49.0, "overall_fit": 57.5, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON><PERSON><PERSON> has basic programming knowledge but lacks experience in required skills.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Version control systems like Git"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor of Technology in Computer Science from GMRIT, Rajam (2024) with CGPA: 6.8/10"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": true, "first_job_start_year": 0, "last_job_end_year": 0, "total_jobs_count": 0, "average_job_change": "No job changes"}, "assessment": {"strengths": ["Basic programming knowledge", "Completed final year project"], "weaknesses": ["Limited technical skills and experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Problem-solving and algorithmic thinking", "Version control systems like Git"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 24.45087480545044, "processed_at": "2025-07-29T10:32:10.449262", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\Meera_Patel_Resume.pdf", "file_size": 0, "word_count": 58, "success": true, "error": null}, "summary": "<PERSON><PERSON> is a Computer Science graduate with basic programming knowledge seeking an entry-level position to grow technical skills. She has completed her final year project and has some exposure to Python, C, and MySQL."}, {"success": true, "filename": "<PERSON><PERSON>_<PERSON>_Resume.pdf", "candidate_name": "<PERSON><PERSON>", "email": "<EMAIL>", "contact_number": "09345612780", "scores": {"final_score": 53.18, "skills_match": 66.**************, "experience_score": 20.0, "education_score": 85.0, "keywords_match": 34.0, "overall_fit": 45.0, "growth_potential": 60.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON><PERSON><PERSON> has basic programming knowledge, but lacks experience and technical skills required for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "SQL (implies MySQL)"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Backend development experience", "Data-driven applications experience"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor of Computer Applications from Osmania University, Hyderabad (2024) CGPA: 7.2/10"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": true, "first_job_start_year": 0, "last_job_end_year": 0, "total_jobs_count": 0, "average_job_change": "No job changes"}, "assessment": {"strengths": ["Basic programming knowledge", "Participated in college coding competition"], "weaknesses": ["Limited technical skills and experience", "No real-world projects to build portfolio"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills and experience", "Problem-solving and algorithmic thinking"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 24.72642707824707, "processed_at": "2025-07-29T10:32:35.227465", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\Priya_Sharma_Resume.pdf", "file_size": 0, "word_count": 58, "success": true, "error": null}, "summary": "Recent graduate with basic programming knowledge looking to start career in software development."}, {"success": true, "filename": "RAJESH_SINGH_Resume.pdf", "candidate_name": "<PERSON><PERSON>", "email": "<EMAIL>", "contact_number": "09001122334", "scores": {"final_score": 85.55, "skills_match": 100, "experience_score": 60.0, "education_score": 100, "keywords_match": 70.33333333333333, "overall_fit": 60.0, "growth_potential": 80.0}, "recommendation": {"decision": "HIRE", "reason": "Candidate has potential but lacks experience in required areas.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "Git"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Backend development experience", "Data-driven applications experience"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["B.Tech in Computer Science from ABC Engineering College (2024) CGPA: 7.8"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": true, "first_job_start_year": 0, "last_job_end_year": 0, "total_jobs_count": 0, "average_job_change": "No job changes"}, "assessment": {"strengths": ["Familiarity with Python and MySQL", "Basic knowledge of Git"], "weaknesses": ["Lack of experience in backend development and data-driven applications", "Limited problem-solving and algorithmic thinking skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Problem-solving skills", "Algorithmic thinking", "Backend development experience"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 29.336202144622803, "processed_at": "2025-07-29T10:33:04.593714", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\RAJESH_SINGH_Resume.pdf", "file_size": 0, "word_count": 88, "success": true, "error": null}, "summary": "<PERSON><PERSON> is a computer science student with basic knowledge of Python, MySQL, and Git. He has completed an online Python course and worked on some college projects. However, he lacks experience in backend development and data-driven applications."}, {"success": true, "filename": "<PERSON><PERSON>_Babu_Resume.pdf", "candidate_name": "<PERSON><PERSON>", "email": "<EMAIL>", "contact_number": "09012345678", "scores": {"final_score": 93.2, "skills_match": 100, "experience_score": 95.0, "education_score": 85.0, "keywords_match": 75.**************, "overall_fit": 87.0, "growth_potential": 80.0}, "recommendation": {"decision": "HIRE", "reason": "Candi<PERSON> has relevant experience and skills, but lacks direct experience in Python development.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "Git"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["Senior Software Developer with 7+ years of experience primarily in Java and Node.js. Recently transitioning to Python development with strong database and system design skills."], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor of Technology in Computer Science JNTU Hyderabad (2017) CGPA: 8.2/10"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": false, "first_job_start_year": 2017, "last_job_end_year": 2025, "total_jobs_count": 2, "average_job_change": "4 years"}, "assessment": {"strengths": ["Strong database and system design skills", "Experience with Java, Node.js, JavaScript, MySQL, MongoDB, Spring Boot, Express.js, Git, Docker, AWS"], "weaknesses": ["Limited experience in Python development (Learning)", "No direct experience with OOP concepts in Python"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Python fundamentals", "OOP concepts in Python", "Database design"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 40.01780438423157, "processed_at": "2025-07-29T10:33:44.677926", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\Suresh_Babu_Resume.pdf", "file_size": 0, "word_count": 147, "success": true, "error": null}, "summary": "Senior Software Developer with 7+ years of experience primarily in Java and Node.js. Recently transitioning to Python development with strong database and system design skills."}, {"success": true, "filename": "<PERSON><PERSON><PERSON>_<PERSON><PERSON>a_Resume.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "contact_number": "08899776655", "scores": {"final_score": 95.4, "skills_match": 100, "experience_score": 95.0, "education_score": 100, "keywords_match": 78.33333333333333, "overall_fit": 93.0, "growth_potential": 80.0}, "recommendation": {"decision": "HIRE", "reason": "Strong technical skills, relevant experience, and good education background.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "Git"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["Senior Python Developer at TechMahindra, Hyderabad Jul 2021 - Present", "Python Developer at Infosys, Bangalore Aug 2018 - Jun 2021"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Master of Technology in Computer Science NIT Warangal (2018) CGPA: 8.9/10"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": false, "first_job_start_year": 2018, "last_job_end_year": 2025, "total_jobs_count": 2, "average_job_change": "3 years 6 months"}, "assessment": {"strengths": ["Proven track record of delivering scalable solutions using Python and MySQL", "Team leadership experience", "Code review skills"], "weaknesses": ["Limited exposure to basic MySQL queries, joins, and indexing", "No explicit mention of OOP concepts in Python"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Problem-solving and algorithmic thinking", "Team leadership experience"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 48.85941767692566, "processed_at": "2025-07-29T10:34:33.583868", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\Vikram_Aditya_Resume.pdf", "file_size": 0, "word_count": 161, "success": true, "error": null}, "summary": "Experienced Python Developer with 6+ years of expertise in backend development, database design, and team leadership. Proven track record of delivering scalable solutions using Python and MySQL."}, {"success": true, "filename": "<PERSON><PERSON>_<PERSON>_Resume.pdf", "candidate_name": "<PERSON><PERSON>", "email": "<EMAIL>", "contact_number": "09876102345", "scores": {"final_score": 72.48, "skills_match": 66.**************, "experience_score": 85.0, "education_score": 90.0, "keywords_match": 59.***************, "overall_fit": 85.0, "growth_potential": 90.0}, "recommendation": {"decision": "CONSIDER", "reason": "<PERSON><PERSON><PERSON> has strong technical skills in Java development and database management but lacks direct experience with Python and related technologies.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["MySQL", "Git"], "missing_skills": ["Python"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": ["Banking System Application: Comprehensive banking system with account management, transaction processing, and reporting features implemented using Java and MySQL.", "Chat Application: Real-time chat application with user authentication and message persistence using Java socket programming."], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor of Technology in Computer Science, IIT Tirupati (2024) - CGPA: 8.7/10"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": true, "first_job_start_year": 0, "last_job_end_year": 0, "total_jobs_count": 0, "average_job_change": "No job changes"}, "assessment": {"strengths": ["Creative problem-solver", "Strong technical skills in Java development and database management.", "Active in competitive programming with a passion for algorithm optimization and system design."], "weaknesses": ["Limited experience with Python", "No direct experience with Spring Framework, Maven, JUnit, Design Patterns"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Python fundamentals", "MySQL queries, joins, and indexing"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 35.894142866134644, "processed_at": "2025-07-29T10:35:09.521132", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\Amit_Kumar_Resume.pdf", "file_size": 0, "word_count": 117, "success": true, "error": null}, "summary": "<PERSON><PERSON> is a creative problem-solver with strong technical skills in Java development and database management. He has experience working on real-world projects and participating in competitive programming."}, {"success": true, "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "email": "<EMAIL>", "contact_number": "540-999-8048", "scores": {"final_score": 92.65, "skills_match": 100, "experience_score": 95.0, "education_score": 90.0, "keywords_match": 69.33333333333333, "overall_fit": 85.0, "growth_potential": 80.0}, "recommendation": {"decision": "HIRE", "reason": "Strong technical skills but lacks experience in required technologies.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "GitHub (implies Git)"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["Automation system using Python, TaskManager, and Arcesium APIs.", "Wrote shell scripts to automate steps performed to recover failed Jshs."], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor of Technology - BTech, Computer and communication (2018 - 2022)"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": false, "first_job_start_year": 2021, "last_job_end_year": 2025, "total_jobs_count": 3, "average_job_change": "1 year 3 months"}, "assessment": {"strengths": ["Problem-solving skills", "Algorithmic thinking", "Public speaking skills"], "weaknesses": ["Lack of experience with Django/Flask, Spring, and cloud platforms"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Problem-solving skills", "Algorithmic thinking"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 58.913471698760986, "processed_at": "2025-07-29T10:36:08.532642", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": "<PERSON><PERSON><PERSON><PERSON> is a software engineer with strong problem-solving and algorithmic thinking skills. She has experience working with Python, MySQL, and Git, but lacks experience in required technologies such as Django/Flask, Spring, and cloud platforms."}, {"success": true, "filename": "VENKATA_JAYA_SIVARAM_GAJIVELLI_RESUME.docx", "candidate_name": "VENKATA JAYA SIVARAM GAJIVELLI", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "contact_number": "918008760311", "scores": {"final_score": 90.65, "skills_match": 100, "experience_score": 75.0, "education_score": 100, "keywords_match": 74.33333333333333, "overall_fit": 90.0, "growth_potential": 90.0}, "recommendation": {"decision": "HIRE", "reason": "Strong foundation in relevant skills, passion for designing secure and efficient solutions, and potential for growth into full-stack development roles.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "Git"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["Cybersecurity Intern Supraja Technologies Pvt. Ltd, 3 Months", "Web Development Intern Swecha Organization, 2 Months"], "experience_gaps": ["No experience in backend development with focus on clean data structures and MySQL queries"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology from Potti Sriramulu Chalava<PERSON> Rao College of Engineering & Technology Vijayawada CGPA: 8.11 Sept 2022 - Present"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": true, "first_job_start_year": 0, "last_job_end_year": 0, "total_jobs_count": 0, "average_job_change": "No job changes"}, "assessment": {"strengths": ["Strong foundation in cybersecurity, web development, and embedded systems", "Passionate about designing secure and efficient solutions"], "weaknesses": ["Limited experience in backend development with focus on clean data structures and MySQL queries"], "red_flags": [], "cultural_fit_indicators": ["Collaborative work culture", "Mentorship from senior developers"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development with focus on clean data structures and MySQL queries", "Problem-solving and algorithmic thinking"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 48.24341654777527, "processed_at": "2025-07-29T10:36:56.849752", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\VENKATA_JAYA_SIVARAM_GAJIVELLI_RESUME.docx", "file_size": 0, "word_count": 302, "success": true, "error": null}, "summary": "Enthusiastic and detail-oriented Computer Science Engineering student with a strong foundation in cybersecurity, web development, and embedded systems. Passionate about designing secure and efficient solutions, leveraging cutting-edge technologies, and contributing to innovative projects that solve real-world challenges."}, {"success": true, "filename": "MAMADI_JOVEL.docx", "candidate_name": "MAMADI JOVEL", "email": "<EMAIL>", "contact_number": "919701179320", "scores": {"final_score": 26.67, "skills_match": 33.33333333333333, "experience_score": 75.0, "education_score": 80.0, "keywords_match": 79.0, "overall_fit": 88.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills, relevant experience, and good education background.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python"], "missing_skills": ["MySQL", "Git"], "skill_match_percentage": 33.3}, "experience_analysis": {"matching_experience": ["Web application on WEATHER PATTERNS", "Project on SECURITY POLICY MANAGEMENT"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["B. Tech (CSE) from 65 PSCMRCET JNTUK September 2022 Present", "Diploma (CIVIL) from Dasari Jhansi Rani College Of Engineering And technology July 2020 March 2022"], "education_level": "STANDARD"}, "job_analysis": {"fresher": true, "first_job_start_year": 0, "last_job_end_year": 0, "total_jobs_count": 0, "average_job_change": "No job changes"}, "assessment": {"strengths": ["Team working capability", "Exploring mobile and Web Technologies", "Problem-solving skills"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Participated in IoT Inventors Hackathon by Eduino Robots in July 2024.", "Represented at the District Level Tennikoit player."]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Backend development", "Data-driven applications"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 40.7570583820343, "processed_at": "2025-07-29T10:37:37.674907", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\MAMADI_JOVEL.docx", "file_size": 0, "word_count": 374, "success": true, "error": null}, "summary": "Highly motivated fresher with a strong foundation in programming languages like Python, C, JAVA, HTML, CSS, JAVASCRIPT. Good problem-solving skills and team working capability."}, {"success": true, "filename": "Satish_Thota.pdf", "candidate_name": "THOTA SATISH BABU", "email": "<EMAIL>", "contact_number": "919492287814", "scores": {"final_score": 92.6, "skills_match": 100, "experience_score": 75.0, "education_score": 100, "keywords_match": 90.**************, "overall_fit": 80.0, "growth_potential": 80.0}, "recommendation": {"decision": "HIRE", "reason": "Strong technical skills, relevant experience, and good education background.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "Git"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["SecurityPolicyManagementSystem (Github)", "AIVOICEAssistantrobot", "ESP32-CAMTelegramBot", "ControllingAppliances"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Bachelor of Technology in Computer Science Engineering (CSE) from Potti Sriramulu Chaluvadi Mall<PERSON>r<PERSON> Rao College of Engineering and Technology"], "education_level": "ADVANCED"}, "job_analysis": {"fresher": true, "first_job_start_year": 0, "last_job_end_year": 0, "total_jobs_count": 0, "average_job_change": "No job changes"}, "assessment": {"strengths": ["Fast learner, adaptability, leadership qualities, problem-solving skills, teamwork skills"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Friendly, collaborative work culture", "Mentorship from senior developers"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Problem-solving skills", "Teamwork skills", "Leadership qualities"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 50.8873827457428, "processed_at": "2025-07-29T10:38:28.772120", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Online_Shopping_Project_Django_Development\\static\\ResumeParser\\resumes\\PY\\Satish_Thota.pdf", "file_size": 0, "word_count": 64, "success": true, "error": null}, "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is a fresher with strong technical skills in Python, MySQL, Git, and problem-solving. He has relevant experience in building projects like SecurityPolicyManagementSystem, AIVOICEAssistantrobot, ESP32-CAMTelegramBot, and ControllingAppliances. He has good education background with a Bachelor of Technology in Computer Science Engineering (CSE) from Potti Sriramulu Chaluvadi Mallikarjuna Rao College of Engineering and Technology."}]