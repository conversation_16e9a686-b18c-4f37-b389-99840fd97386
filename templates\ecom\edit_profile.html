{% extends 'ecom/customer_base.html' %}
{% load widget_tweaks %}
{% block content %}

<head>
  <style media="screen">
    body {
      background-color:#ffffff;
    }
    a:link {
      text-decoration: none;
    }

    .note {
      text-align: center;
      height: 80px;
      background: -webkit-linear-gradient(left, #0072ff, #8811c5);
      color: #fff;
      font-weight: bold;
      line-height: 80px;
    }

    .form-content {
      padding: 5%;
      border: 1px solid #ced4da;
      margin-bottom: 2%;
    }

    .form-control {
      border-radius: 1.5rem;
    }

    .btnSubmit {
      border: none;
      border-radius: 1.5rem;
      padding: 1%;
      width: 20%;
      cursor: pointer;
      background: #0062cc;
      color: #fff;
    }

    .menu{
      top: 50px;
    }
  </style>


</head>
<br><br><br><br><br><br>
<form method="post" enctype="multipart/form-data">
  {% csrf_token %}
  <div class="container register-form">
    <div class="form">
      <div class="note">
        <p>EDIT PROFILE</p>
      </div>
      <div class="form-content">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              {% render_field userForm.first_name class="form-control" placeholder="First Name" %}
            </div>
            <div class="form-group">
              {% render_field userForm.username class="form-control" placeholder="Username" %}
            </div>
            <div class="form-group">
              {% render_field customerForm.mobile class="form-control" placeholder="Mobile" %}
            </div>
            <div class="form-group">
              {% render_field customerForm.profile_pic class="form-control" placeholder="Profile Picture" %}
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              {% render_field userForm.last_name class="form-control" placeholder="Last Name" %}
            </div>
            <div class="form-group">
              {% render_field userForm.password class="form-control" placeholder="Password" %}
            </div>
            <div class="form-group">
              {% render_field customerForm.address class="form-control" placeholder="Address" %}
            </div>

          </div>
        </div>
        <button type="submit" class="btnSubmit">Edit</button>
      </div>
    </div>
  </div>
</form>
{% endblock content %}
