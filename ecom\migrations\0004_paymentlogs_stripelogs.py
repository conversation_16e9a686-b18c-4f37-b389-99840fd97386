# Generated by Django 3.0.5 on 2025-06-02 04:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ecom', '0003_auto_20250602_0952'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentLogs',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_type', models.Char<PERSON>ield(max_length=20)),
                ('user_id', models.IntegerField()),
                ('order_id', models.CharField(max_length=250)),
                ('amount', models.PositiveIntegerField()),
                ('date', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='StripeLogs',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stripe_id', models.Cha<PERSON><PERSON><PERSON>(max_length=250, unique=True)),
                ('amount', models.PositiveIntegerField()),
                ('name', models.Char<PERSON>ield(max_length=250)),
                ('email', models.Char<PERSON>ield(max_length=250)),
            ],
        ),
    ]
