<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Django E-Commerce Project Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 25px;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        .feature-list {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        .toc {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 5px 0;
        }
        .toc a {
            text-decoration: none;
            color: #3498db;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Django E-Commerce Project Documentation</h1>

        <div class="toc">
            <h2>Table of Contents</h2>
            <ul>
                <li><a href="#overview">1. Project Overview</a></li>
                <li><a href="#installation">2. Installation & Setup</a></li>
                <li><a href="#architecture">3. Project Architecture</a></li>
                <li><a href="#models">4. Database Models</a></li>
                <li><a href="#views">5. Views & URL Patterns</a></li>
                <li><a href="#templates">6. Templates & Frontend</a></li>
                <li><a href="#forms">7. Forms</a></li>
                <li><a href="#admin">8. Admin Interface</a></li>
                <li><a href="#features">9. Features</a></li>
                <li><a href="#payment">10. Payment Integration</a></li>
                <li><a href="#usage">11. Usage Guide</a></li>
                <li><a href="#troubleshooting">12. Troubleshooting</a></li>
            </ul>
        </div>

        <section id="overview">
            <h2>1. Project Overview</h2>
            <p>This is a comprehensive Django-based e-commerce web application that provides a complete online shopping experience. The project includes both customer-facing features and administrative functionality for managing the online store.</p>

            <div class="feature-list">
                <h3>Key Features:</h3>
                <ul>
                    <li>User registration and authentication system</li>
                    <li>Product catalog with search functionality</li>
                    <li>Shopping cart management</li>
                    <li>Order processing and tracking</li>
                    <li>Payment integration with Stripe</li>
                    <li>Admin dashboard for store management</li>
                    <li>Customer feedback system</li>
                    <li>Invoice generation and download</li>
                    <li>Email notifications</li>
                    <li>Responsive design</li>
                </ul>
            </div>

            <div class="highlight">
                <strong>Technology Stack:</strong>
                <ul>
                    <li>Backend: Django 3.0.5</li>
                    <li>Database: SQLite (default)</li>
                    <li>Frontend: HTML, CSS, Bootstrap</li>
                    <li>Payment: Stripe API</li>
                    <li>PDF Generation: xhtml2pdf</li>
                    <li>Email: SMTP</li>
                </ul>
            </div>
        </section>

        <section id="installation">
            <h2>2. Installation & Setup</h2>

            <h3>2.1 Prerequisites</h3>
            <ul>
                <li>Python 3.7 or higher</li>
                <li>pip (Python package installer)</li>
                <li>Virtual environment (recommended)</li>
            </ul>

            <h3>2.2 Installation Steps</h3>
            <div class="code-block">
# 1. Clone or download the project
cd Online_Shopping_Project_Django_Development

# 2. Create virtual environment
python -m venv venv

# 3. Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# 4. Install dependencies
pip install -r requirement.txt

# 5. Run migrations
python manage.py makemigrations
python manage.py migrate

# 6. Create superuser (optional)
python manage.py createsuperuser

# 7. Run the development server
python manage.py runserver
            </div>

            <h3>2.3 Configuration</h3>
            <div class="warning">
                <strong>Important Configuration Steps:</strong>
                <ul>
                    <li>Update email settings in settings.py for contact form functionality</li>
                    <li>Configure Stripe API keys for payment processing</li>
                    <li>Set DEBUG=False for production deployment</li>
                    <li>Configure ALLOWED_HOSTS for production</li>
                </ul>
            </div>
        </section>

        <section id="architecture">
            <h2>3. Project Architecture</h2>

            <h3>3.1 Project Structure</h3>
            <div class="code-block">
Online_Shopping_Project_Django_Development/
├── ecommerce/              # Main project directory
│   ├── __init__.py
│   ├── settings.py         # Project settings
│   ├── urls.py            # Main URL configuration
│   ├── wsgi.py            # WSGI configuration
│   └── asgi.py            # ASGI configuration
├── ecom/                  # Main application
│   ├── models.py          # Database models
│   ├── views.py           # View functions
│   ├── forms.py           # Form definitions
│   ├── admin.py           # Admin configuration
│   ├── apps.py            # App configuration
│   └── migrations/        # Database migrations
├── templates/ecom/        # HTML templates
├── static/                # Static files (CSS, JS, images)
├── manage.py              # Django management script
├── requirement.txt        # Project dependencies
└── db.sqlite3            # SQLite database
            </div>

            <h3>3.2 Application Flow</h3>
            <p>The application follows Django's MVT (Model-View-Template) architecture:</p>
            <ul>
                <li><strong>Models:</strong> Define database structure and business logic</li>
                <li><strong>Views:</strong> Handle HTTP requests and business logic</li>
                <li><strong>Templates:</strong> Render HTML responses</li>
                <li><strong>URLs:</strong> Map URLs to appropriate views</li>
            </ul>
        </section>

        <section id="models">
            <h2>4. Database Models</h2>

            <h3>4.1 Customer Model</h3>
            <div class="code-block">
class Customer(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    profile_pic = models.ImageField(upload_to='profile_pic/CustomerProfilePic/', null=True, blank=True)
    address = models.CharField(max_length=40)
    mobile = models.CharField(max_length=20, null=False)

    @property
    def get_name(self):
        return self.user.first_name + " " + self.user.last_name

    @property
    def get_id(self):
        return self.user.id

    def __str__(self):
        return self.user.first_name
            </div>
            <p><strong>Purpose:</strong> Extends Django's built-in User model with additional customer-specific fields like address, mobile number, and profile picture.</p>

            <h3>4.2 Product Model</h3>
            <div class="code-block">
class Product(models.Model):
    name = models.CharField(max_length=40)
    product_image = models.ImageField(upload_to='product_image/', null=True, blank=True)
    price = models.PositiveIntegerField()
    description = models.CharField(max_length=40)

    def __str__(self):
        return self.name
            </div>
            <p><strong>Purpose:</strong> Represents products in the store with basic information like name, image, price, and description.</p>

            <h3>4.3 Orders Model</h3>
            <div class="code-block">
class Orders(models.Model):
    STATUS = (
        ('Pending', 'Pending'),
        ('Order Confirmed', 'Order Confirmed'),
        ('Out for Delivery', 'Out for Delivery'),
        ('Delivered', 'Delivered'),
    )
    customer = models.ForeignKey('Customer', on_delete=models.CASCADE, null=True)
    product = models.ForeignKey('Product', on_delete=models.CASCADE, null=True)
    email = models.CharField(max_length=50, null=True)
    address = models.CharField(max_length=500, null=True)
    mobile = models.CharField(max_length=20, null=True)
    order_date = models.DateField(auto_now_add=True, null=True)
    status = models.CharField(max_length=50, null=True, choices=STATUS)
            </div>
            <p><strong>Purpose:</strong> Manages customer orders with status tracking, delivery information, and order history.</p>

            <h3>4.4 Feedback Model</h3>
            <div class="code-block">
class Feedback(models.Model):
    name = models.CharField(max_length=40)
    feedback = models.CharField(max_length=500)
    date = models.DateField(auto_now_add=True, null=True)

    def __str__(self):
        return self.name
            </div>
            <p><strong>Purpose:</strong> Stores customer feedback and reviews for the store.</p>

            <h3>4.5 Database Relationships</h3>
            <table>
                <tr>
                    <th>Model</th>
                    <th>Relationship</th>
                    <th>Related Model</th>
                    <th>Type</th>
                </tr>
                <tr>
                    <td>Customer</td>
                    <td>user</td>
                    <td>User</td>
                    <td>OneToOne</td>
                </tr>
                <tr>
                    <td>Orders</td>
                    <td>customer</td>
                    <td>Customer</td>
                    <td>ForeignKey</td>
                </tr>
                <tr>
                    <td>Orders</td>
                    <td>product</td>
                    <td>Product</td>
                    <td>ForeignKey</td>
                </tr>
            </table>
        </section>

        <section id="views">
            <h2>5. Views & URL Patterns</h2>

            <h3>5.1 Public Views</h3>
            <table>
                <tr>
                    <th>URL Pattern</th>
                    <th>View Function</th>
                    <th>Purpose</th>
                </tr>
                <tr>
                    <td>/</td>
                    <td>home_view</td>
                    <td>Homepage with product listing</td>
                </tr>
                <tr>
                    <td>/search</td>
                    <td>search_view</td>
                    <td>Product search functionality</td>
                </tr>
                <tr>
                    <td>/aboutus</td>
                    <td>aboutus_view</td>
                    <td>About us page</td>
                </tr>
                <tr>
                    <td>/contactus</td>
                    <td>contactus_view</td>
                    <td>Contact form</td>
                </tr>
                <tr>
                    <td>/send-feedback</td>
                    <td>send_feedback_view</td>
                    <td>Customer feedback form</td>
                </tr>
            </table>

            <h3>5.2 Customer Views</h3>
            <table>
                <tr>
                    <th>URL Pattern</th>
                    <th>View Function</th>
                    <th>Purpose</th>
                </tr>
                <tr>
                    <td>/customersignup</td>
                    <td>customer_signup_view</td>
                    <td>Customer registration</td>
                </tr>
                <tr>
                    <td>/customerlogin</td>
                    <td>LoginView</td>
                    <td>Customer login</td>
                </tr>
                <tr>
                    <td>/customer-home</td>
                    <td>customer_home_view</td>
                    <td>Customer dashboard</td>
                </tr>
                <tr>
                    <td>/my-order</td>
                    <td>my_order_view</td>
                    <td>Order history</td>
                </tr>
                <tr>
                    <td>/my-profile</td>
                    <td>my_profile_view</td>
                    <td>Customer profile</td>
                </tr>
                <tr>
                    <td>/edit-profile</td>
                    <td>edit_profile_view</td>
                    <td>Edit customer profile</td>
                </tr>
            </table>

            <h3>5.3 Shopping Cart Views</h3>
            <table>
                <tr>
                    <th>URL Pattern</th>
                    <th>View Function</th>
                    <th>Purpose</th>
                </tr>
                <tr>
                    <td>/add-to-cart/&lt;int:pk&gt;</td>
                    <td>add_to_cart_view</td>
                    <td>Add product to cart</td>
                </tr>
                <tr>
                    <td>/cart</td>
                    <td>cart_view</td>
                    <td>View shopping cart</td>
                </tr>
                <tr>
                    <td>/remove-from-cart/&lt;int:pk&gt;</td>
                    <td>remove_from_cart_view</td>
                    <td>Remove product from cart</td>
                </tr>
                <tr>
                    <td>/customer-address</td>
                    <td>customer_address_view</td>
                    <td>Shipping address form</td>
                </tr>
                <tr>
                    <td>/payment-success</td>
                    <td>payment_success_view</td>
                    <td>Payment confirmation</td>
                </tr>
            </table>

            <h3>5.4 Admin Views</h3>
            <table>
                <tr>
                    <th>URL Pattern</th>
                    <th>View Function</th>
                    <th>Purpose</th>
                </tr>
                <tr>
                    <td>/adminlogin</td>
                    <td>LoginView</td>
                    <td>Admin login</td>
                </tr>
                <tr>
                    <td>/admin-dashboard</td>
                    <td>admin_dashboard_view</td>
                    <td>Admin dashboard</td>
                </tr>
                <tr>
                    <td>/view-customer</td>
                    <td>view_customer_view</td>
                    <td>Customer management</td>
                </tr>
                <tr>
                    <td>/admin-products</td>
                    <td>admin_products_view</td>
                    <td>Product management</td>
                </tr>
                <tr>
                    <td>/admin-view-booking</td>
                    <td>admin_view_booking_view</td>
                    <td>Order management</td>
                </tr>
                <tr>
                    <td>/view-feedback</td>
                    <td>view_feedback_view</td>
                    <td>View customer feedback</td>
                </tr>
            </table>
        </section>

        <section id="templates">
            <h2>6. Templates & Frontend</h2>

            <h3>6.1 Template Structure</h3>
            <p>The application uses Django's template system with a hierarchical structure:</p>

            <div class="code-block">
templates/ecom/
├── homebase.html          # Base template for public pages
├── customer_base.html     # Base template for customer pages
├── admin_base.html        # Base template for admin pages
├── navbar.html           # Public navigation
├── customer_navbar.html  # Customer navigation
├── footer.html           # Footer component
├── index.html            # Homepage
├── customer_home.html    # Customer dashboard
├── admin_dashboard.html  # Admin dashboard
├── cart.html             # Shopping cart
├── my_order.html         # Order history
├── payment_success.html  # Payment confirmation
└── ... (other templates)
            </div>

            <h3>6.2 Key Templates</h3>
            <table>
                <tr>
                    <th>Template</th>
                    <th>Purpose</th>
                    <th>Features</th>
                </tr>
                <tr>
                    <td>index.html</td>
                    <td>Homepage</td>
                    <td>Product grid, search, cart counter</td>
                </tr>
                <tr>
                    <td>customer_home.html</td>
                    <td>Customer dashboard</td>
                    <td>Personalized product view</td>
                </tr>
                <tr>
                    <td>cart.html</td>
                    <td>Shopping cart</td>
                    <td>Product list, total calculation, checkout</td>
                </tr>
                <tr>
                    <td>admin_dashboard.html</td>
                    <td>Admin panel</td>
                    <td>Statistics, recent orders, management links</td>
                </tr>
                <tr>
                    <td>my_order.html</td>
                    <td>Order history</td>
                    <td>Order tracking, invoice download</td>
                </tr>
            </table>

            <h3>6.3 Frontend Features</h3>
            <div class="feature-list">
                <ul>
                    <li>Responsive design using Bootstrap</li>
                    <li>Product cards with hover effects</li>
                    <li>Shopping cart with real-time counter</li>
                    <li>Search functionality</li>
                    <li>User authentication forms</li>
                    <li>Admin dashboard with statistics</li>
                    <li>Order tracking interface</li>
                    <li>Profile management</li>
                </ul>
            </div>
        </section>

        <section id="forms">
            <h2>7. Forms</h2>

            <h3>7.1 Customer Forms</h3>
            <div class="code-block">
# Customer Registration Forms
class CustomerUserForm(forms.ModelForm):
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'username', 'password']
        widgets = {'password': forms.PasswordInput()}

class CustomerForm(forms.ModelForm):
    class Meta:
        model = models.Customer
        fields = ['address', 'mobile', 'profile_pic']
            </div>

            <h3>7.2 Product Management Forms</h3>
            <div class="code-block">
class ProductForm(forms.ModelForm):
    class Meta:
        model = models.Product
        fields = ['name', 'price', 'description', 'product_image']
            </div>

            <h3>7.3 Order and Address Forms</h3>
            <div class="code-block">
# Shipping Address Form
class AddressForm(forms.Form):
    Email = forms.EmailField()
    Mobile = forms.IntegerField()
    Address = forms.CharField(max_length=500)

# Order Status Update Form
class OrderForm(forms.ModelForm):
    class Meta:
        model = models.Orders
        fields = ['status']
            </div>

            <h3>7.4 Communication Forms</h3>
            <div class="code-block">
# Feedback Form
class FeedbackForm(forms.ModelForm):
    class Meta:
        model = models.Feedback
        fields = ['name', 'feedback']

# Contact Us Form
class ContactusForm(forms.Form):
    Name = forms.CharField(max_length=30)
    Email = forms.EmailField()
    Message = forms.CharField(max_length=500, widget=forms.Textarea(attrs={'rows': 3, 'cols': 30}))
            </div>
        </section>

        <section id="admin">
            <h2>8. Admin Interface</h2>

            <h3>8.1 Admin Configuration</h3>
            <p>The application includes a comprehensive admin interface for managing all aspects of the e-commerce store:</p>

            <div class="code-block">
# admin.py
from django.contrib import admin
from .models import Customer, Product, Orders, Feedback

class CustomerAdmin(admin.ModelAdmin):
    pass
admin.site.register(Customer, CustomerAdmin)

class ProductAdmin(admin.ModelAdmin):
    pass
admin.site.register(Product, ProductAdmin)

class OrderAdmin(admin.ModelAdmin):
    pass
admin.site.register(Orders, OrderAdmin)

class FeedbackAdmin(admin.ModelAdmin):
    pass
admin.site.register(Feedback, FeedbackAdmin)
            </div>

            <h3>8.2 Admin Dashboard Features</h3>
            <div class="feature-list">
                <ul>
                    <li><strong>Customer Management:</strong> View, edit, and delete customer accounts</li>
                    <li><strong>Product Management:</strong> Add, update, and remove products</li>
                    <li><strong>Order Management:</strong> Track and update order status</li>
                    <li><strong>Feedback Management:</strong> View customer feedback and reviews</li>
                    <li><strong>Statistics:</strong> Dashboard with key metrics</li>
                    <li><strong>Inventory Control:</strong> Product stock management</li>
                </ul>
            </div>

            <h3>8.3 Admin Permissions</h3>
            <p>The admin interface uses Django's built-in authentication system with the following access controls:</p>
            <ul>
                <li>Only superusers can access the Django admin panel</li>
                <li>Custom admin views require admin login</li>
                <li>Role-based access control for different admin functions</li>
            </ul>
        </section>

        <section id="features">
            <h2>9. Features</h2>

            <h3>9.1 Customer Features</h3>
            <div class="feature-list">
                <h4>Authentication & Profile Management</h4>
                <ul>
                    <li>User registration with profile picture upload</li>
                    <li>Secure login/logout functionality</li>
                    <li>Profile editing and management</li>
                    <li>Password protection</li>
                </ul>

                <h4>Shopping Experience</h4>
                <ul>
                    <li>Browse products with detailed information</li>
                    <li>Search products by name</li>
                    <li>Add/remove items from shopping cart</li>
                    <li>Real-time cart counter</li>
                    <li>Secure checkout process</li>
                </ul>

                <h4>Order Management</h4>
                <ul>
                    <li>Place orders with shipping information</li>
                    <li>Track order status (Pending, Confirmed, Out for Delivery, Delivered)</li>
                    <li>View order history</li>
                    <li>Download invoices in PDF format</li>
                </ul>

                <h4>Communication</h4>
                <ul>
                    <li>Send feedback to store administrators</li>
                    <li>Contact form for customer support</li>
                    <li>Email notifications</li>
                </ul>
            </div>

            <h3>9.2 Admin Features</h3>
            <div class="feature-list">
                <h4>Dashboard & Analytics</h4>
                <ul>
                    <li>Comprehensive admin dashboard</li>
                    <li>Customer count statistics</li>
                    <li>Product inventory overview</li>
                    <li>Order statistics and recent orders</li>
                </ul>

                <h4>Customer Management</h4>
                <ul>
                    <li>View all registered customers</li>
                    <li>Edit customer information</li>
                    <li>Delete customer accounts</li>
                    <li>Customer profile management</li>
                </ul>

                <h4>Product Management</h4>
                <ul>
                    <li>Add new products with images</li>
                    <li>Edit existing product details</li>
                    <li>Delete products from inventory</li>
                    <li>Product image management</li>
                </ul>

                <h4>Order Management</h4>
                <ul>
                    <li>View all customer orders</li>
                    <li>Update order status</li>
                    <li>Delete orders if necessary</li>
                    <li>Order tracking and fulfillment</li>
                </ul>

                <h4>Feedback Management</h4>
                <ul>
                    <li>View customer feedback</li>
                    <li>Monitor customer satisfaction</li>
                    <li>Respond to customer concerns</li>
                </ul>
            </div>
        </section>

        <section id="payment">
            <h2>10. Payment Integration</h2>

            <h3>10.1 Stripe Integration</h3>
            <p>The application integrates with Stripe for secure payment processing:</p>

            <div class="code-block">
import stripe

stripe.api_key = "sk_test_51RQoRBQQ3USwvpS7nCGQ4qInZZgs0pzragwWRZcmLNKVGlBFrSJdVeljLygC0e6jYJ1ZKL0abkkuG1XKeP9XVRhk00toa8tTdN"

# Create checkout session
checkout_session = stripe.checkout.Session.create(
    payment_method_types=['card'],
    line_items=line_items,
    metadata={
        'user_email': email,
        'phone_number': mobile,
        'address': address,
    },
    mode='payment',
    success_url='http://localhost:8000/payment-success',
    cancel_url='http://localhost:8000/cart',
)
            </div>

            <h3>10.2 Payment Flow</h3>
            <ol>
                <li>Customer adds products to cart</li>
                <li>Customer proceeds to checkout</li>
                <li>Customer enters shipping information</li>
                <li>System creates Stripe checkout session</li>
                <li>Customer is redirected to Stripe payment page</li>
                <li>After successful payment, customer is redirected to success page</li>
                <li>Orders are created in the database</li>
                <li>Cart is cleared</li>
            </ol>

            <h3>10.3 Security Features</h3>
            <ul>
                <li>Secure payment processing through Stripe</li>
                <li>No credit card information stored locally</li>
                <li>HTTPS encryption for payment pages</li>
                <li>Payment verification before order creation</li>
            </ul>

            <div class="warning">
                <strong>Important:</strong> The current implementation uses test API keys. For production, you must:
                <ul>
                    <li>Replace test keys with live Stripe API keys</li>
                    <li>Configure proper webhook endpoints</li>
                    <li>Implement proper error handling</li>
                    <li>Set up SSL certificates</li>
                </ul>
            </div>
        </section>

        <section id="usage">
            <h2>11. Usage Guide</h2>

            <h3>11.1 For Customers</h3>

            <h4>Getting Started</h4>
            <ol>
                <li>Visit the homepage at <code>http://localhost:8000/</code></li>
                <li>Browse available products</li>
                <li>Use the search bar to find specific products</li>
                <li>Click "Add to Cart" to add products to your shopping cart</li>
            </ol>

            <h4>Creating an Account</h4>
            <ol>
                <li>Click "Customer Signup" from the navigation</li>
                <li>Fill in your personal information</li>
                <li>Upload a profile picture (optional)</li>
                <li>Provide your address and mobile number</li>
                <li>Click "Sign Up" to create your account</li>
            </ol>

            <h4>Making a Purchase</h4>
            <ol>
                <li>Add desired products to your cart</li>
                <li>Click the cart icon to review your items</li>
                <li>Click "Proceed to Checkout"</li>
                <li>Enter your shipping information</li>
                <li>Complete payment through Stripe</li>
                <li>Receive order confirmation</li>
            </ol>

            <h4>Managing Your Account</h4>
            <ul>
                <li><strong>View Orders:</strong> Go to "My Orders" to see your purchase history</li>
                <li><strong>Edit Profile:</strong> Update your personal information</li>
                <li><strong>Download Invoices:</strong> Click download button next to any order</li>
                <li><strong>Send Feedback:</strong> Use the feedback form to share your experience</li>
            </ul>

            <h3>11.2 For Administrators</h3>

            <h4>Accessing Admin Panel</h4>
            <ol>
                <li>Go to <code>http://localhost:8000/adminclick</code></li>
                <li>Login with admin credentials</li>
                <li>Access the admin dashboard</li>
            </ol>

            <h4>Managing Products</h4>
            <ol>
                <li>Go to "Products" in the admin menu</li>
                <li>Click the "+" button to add new products</li>
                <li>Fill in product details and upload images</li>
                <li>Use edit/delete buttons to manage existing products</li>
            </ol>

            <h4>Managing Orders</h4>
            <ol>
                <li>Go to "Orders" in the admin menu</li>
                <li>View all customer orders</li>
                <li>Update order status as needed</li>
                <li>Delete orders if necessary</li>
            </ol>

            <h4>Customer Management</h4>
            <ol>
                <li>Go to "Customers" in the admin menu</li>
                <li>View customer information</li>
                <li>Edit customer details if needed</li>
                <li>Remove customers if necessary</li>
            </ol>
        </section>

        <section id="troubleshooting">
            <h2>12. Troubleshooting</h2>

            <h3>12.1 Common Installation Issues</h3>

            <h4>Django Import Error</h4>
            <div class="warning">
                <strong>Error:</strong> "Couldn't import Django. Are you sure it's installed?"
                <br><strong>Solution:</strong>
                <ul>
                    <li>Ensure virtual environment is activated</li>
                    <li>Run <code>pip install -r requirement.txt</code></li>
                    <li>Check Python version compatibility</li>
                </ul>
            </div>

            <h4>Database Migration Issues</h4>
            <div class="warning">
                <strong>Error:</strong> Migration errors or database not found
                <br><strong>Solution:</strong>
                <ul>
                    <li>Run <code>python manage.py makemigrations</code></li>
                    <li>Run <code>python manage.py migrate</code></li>
                    <li>Delete db.sqlite3 and migrations if necessary</li>
                    <li>Recreate migrations from scratch</li>
                </ul>
            </div>

            <h4>Static Files Not Loading</h4>
            <div class="warning">
                <strong>Error:</strong> CSS, images, or JavaScript files not loading
                <br><strong>Solution:</strong>
                <ul>
                    <li>Check STATIC_URL and STATICFILES_DIRS in settings.py</li>
                    <li>Run <code>python manage.py collectstatic</code> for production</li>
                    <li>Ensure DEBUG=True for development</li>
                </ul>
            </div>

            <h3>12.2 Payment Integration Issues</h3>

            <h4>Stripe Payment Failures</h4>
            <div class="warning">
                <strong>Error:</strong> Payment processing errors
                <br><strong>Solution:</strong>
                <ul>
                    <li>Verify Stripe API keys are correct</li>
                    <li>Check network connectivity</li>
                    <li>Ensure test mode is enabled for development</li>
                    <li>Check Stripe dashboard for error logs</li>
                </ul>
            </div>

            <h4>Redirect URL Issues</h4>
            <div class="warning">
                <strong>Error:</strong> Payment redirect not working
                <br><strong>Solution:</strong>
                <ul>
                    <li>Update success_url and cancel_url in views.py</li>
                    <li>Ensure URLs are accessible</li>
                    <li>Check for HTTPS requirements in production</li>
                </ul>
            </div>

            <h3>12.3 Email Configuration Issues</h3>

            <h4>Contact Form Email Not Sending</h4>
            <div class="warning">
                <strong>Error:</strong> SMTPAuthenticationError
                <br><strong>Solution:</strong>
                <ul>
                    <li>Update EMAIL_HOST_USER and EMAIL_HOST_PASSWORD in settings.py</li>
                    <li>Enable "Less secure app access" for Gmail</li>
                    <li>Use app-specific passwords for Gmail</li>
                    <li>Check firewall and network settings</li>
                </ul>
            </div>

            <h3>12.4 Performance Issues</h3>

            <h4>Slow Page Loading</h4>
            <div class="highlight">
                <strong>Optimization Tips:</strong>
                <ul>
                    <li>Optimize database queries using select_related() and prefetch_related()</li>
                    <li>Implement caching for frequently accessed data</li>
                    <li>Compress and optimize images</li>
                    <li>Use CDN for static files in production</li>
                </ul>
            </div>

            <h3>12.5 Security Considerations</h3>

            <div class="warning">
                <strong>Production Security Checklist:</strong>
                <ul>
                    <li>Set DEBUG=False</li>
                    <li>Configure ALLOWED_HOSTS</li>
                    <li>Use environment variables for sensitive data</li>
                    <li>Enable HTTPS</li>
                    <li>Update SECRET_KEY</li>
                    <li>Configure proper database permissions</li>
                    <li>Implement rate limiting</li>
                    <li>Regular security updates</li>
                </ul>
            </div>

            <h3>12.6 Development Tips</h3>

            <div class="feature-list">
                <h4>Best Practices</h4>
                <ul>
                    <li>Use virtual environments for dependency management</li>
                    <li>Keep requirements.txt updated</li>
                    <li>Use version control (Git) for code management</li>
                    <li>Write tests for critical functionality</li>
                    <li>Document code changes and features</li>
                    <li>Regular database backups</li>
                    <li>Monitor application logs</li>
                </ul>
            </div>

            <h3>12.7 Getting Help</h3>

            <p>If you encounter issues not covered in this documentation:</p>
            <ul>
                <li>Check Django official documentation: <a href="https://docs.djangoproject.com/">https://docs.djangoproject.com/</a></li>
                <li>Search Stack Overflow for similar issues</li>
                <li>Check the project's issue tracker</li>
                <li>Review Django and Python error logs</li>
                <li>Test in a clean virtual environment</li>
            </ul>
        </section>

        <section id="conclusion">
            <h2>13. Conclusion</h2>

            <p>This Django e-commerce project provides a solid foundation for building online shopping applications. The modular architecture, comprehensive feature set, and detailed documentation make it suitable for both learning purposes and as a starting point for production applications.</p>

            <div class="highlight">
                <h3>Key Takeaways:</h3>
                <ul>
                    <li>The project demonstrates Django best practices and patterns</li>
                    <li>Modular design allows for easy customization and extension</li>
                    <li>Integration with modern payment systems (Stripe)</li>
                    <li>Comprehensive admin interface for store management</li>
                    <li>Responsive design for mobile and desktop users</li>
                    <li>Security considerations for production deployment</li>
                </ul>
            </div>

            <div class="feature-list">
                <h3>Future Enhancements:</h3>
                <ul>
                    <li>Implement product categories and filtering</li>
                    <li>Add product reviews and ratings</li>
                    <li>Implement inventory management</li>
                    <li>Add wishlist functionality</li>
                    <li>Implement discount codes and promotions</li>
                    <li>Add multi-language support</li>
                    <li>Implement advanced analytics</li>
                    <li>Add social media integration</li>
                </ul>
            </div>

            <p><strong>Version:</strong> 1.0<br>
            <strong>Last Updated:</strong> December 2024<br>
            <strong>Django Version:</strong> 3.0.5<br>
            <strong>Python Version:</strong> 3.7+</p>
        </section>
    </div>
</body>
</html>
