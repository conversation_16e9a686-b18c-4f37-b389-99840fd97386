{% extends 'ecom/admin_base.html' %}
{% load static %}
{% block content %}
    {% if jobcreated %}
        <div class="box">
            <h1>Job Information Added Successfully</h1>
            <button onclick="window.location.href='/'">OK</button>
        </div>
    {% else %}
        <div class="box">
            <h1>Something went wrong</h1>
            <button onclick="window.location.href='/'">OK</button>
        </div>
    {% endif %}

    <br><br><br><br><br>
    <br><br><br><br><br>
    <br><br><br><br><br>
    <br><br><br><br><br>
    <br><br><br>


    <style>
        .box {
            width: 500px;
            padding: 40px;
            position: absolute;
            top: 25%;
            left: 50%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            text-align: center;
            transition: all 0.3s ease;
            margin-top: 100px;
            border-radius: 16px;
            box-shadow: 0 16px 48px rgba(26, 35, 126, 0.2);
            transform: translate(-50%, -50%);
        }
        .box h1 {
            color: #1a237e;
            text-transform: uppercase;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .box button {
    border: 0;
    background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
    display: block;
    margin: 20px auto;
    text-align: center;
    padding: 14px 40px;
    outline: none;
    color: white;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-weight: 600;
    font-size: 16px;
    box-shadow: 0 4px 16px rgba(26, 35, 126, 0.3);
}

.box button:hover {
    background: linear-gradient(135deg, #3949ab 0%, #5c6bc0 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(26, 35, 126, 0.4);
}
    </style>
{% endblock content%}