# Generated by Django 3.0.5 on 2025-06-03 01:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ecom', '0009_auto_20250603_0706'),
    ]

    operations = [
        migrations.DeleteModel(
            name='ErrorLogs',
        ),
        migrations.AlterModelOptions(
            name='paymenterrorlogs',
            options={'ordering': ['-timestamp']},
        ),
        migrations.RemoveField(
            model_name='paymenterrorlogs',
            name='customer_mobile',
        ),
        migrations.RemoveField(
            model_name='paymenterrorlogs',
            name='error_category',
        ),
        migrations.RemoveField(
            model_name='paymenterrorlogs',
            name='error_code',
        ),
        migrations.RemoveField(
            model_name='paymenterrorlogs',
            name='gateway_response',
        ),
        migrations.RemoveField(
            model_name='paymenterrorlogs',
            name='gateway_transaction_id',
        ),
        migrations.RemoveField(
            model_name='paymenterrorlogs',
            name='ip_address',
        ),
        migrations.RemoveField(
            model_name='paymenterrorlogs',
            name='request_data',
        ),
        migrations.RemoveField(
            model_name='paymenterrorlogs',
            name='request_method',
        ),
        migrations.RemoveField(
            model_name='paymenterrorlogs',
            name='request_url',
        ),
        migrations.RemoveField(
            model_name='paymenterrorlogs',
            name='resolution_notes',
        ),
        migrations.RemoveField(
            model_name='paymenterrorlogs',
            name='resolved',
        ),
        migrations.RemoveField(
            model_name='paymenterrorlogs',
            name='resolved_at',
        ),
        migrations.RemoveField(
            model_name='paymenterrorlogs',
            name='user_agent',
        ),
        migrations.AlterField(
            model_name='paymenterrorlogs',
            name='error_message',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='paymenterrorlogs',
            name='payment_type',
            field=models.CharField(default='unknown', max_length=20),
        ),
    ]
