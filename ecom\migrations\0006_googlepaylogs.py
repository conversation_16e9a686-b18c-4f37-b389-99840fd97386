# Generated by Django 3.0.5 on 2025-06-02 05:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ecom', '0005_auto_20250602_1031'),
    ]

    operations = [
        migrations.CreateModel(
            name='GooglepayLogs',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token_id', models.CharField(max_length=250)),
                ('card_last4', models.CharField(max_length=250)),
                ('address_city', models.CharField(max_length=250)),
                ('address_country', models.Char<PERSON>ield(max_length=250)),
                ('address_state', models.<PERSON><PERSON><PERSON><PERSON>(max_length=250)),
                ('address_zip', models.Char<PERSON>ield(max_length=250)),
                ('card_brand', models.CharField(max_length=250)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
            ],
        ),
    ]
